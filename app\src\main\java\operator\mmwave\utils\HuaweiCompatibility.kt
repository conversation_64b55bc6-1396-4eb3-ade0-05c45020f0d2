package operator.mmwave.utils

import android.content.Context
import android.os.Build
import android.util.Log

/**
 * 华为设备兼容性工具类
 */
object HuaweiCompatibility {
    
    private const val TAG = "HuaweiCompatibility"
    
    /**
     * 检查是否为华为设备
     */
    fun isHuaweiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.lowercase()
        val brand = Build.BRAND.lowercase()
        return manufacturer.contains("huawei") || 
               manufacturer.contains("honor") ||
               brand.contains("huawei") ||
               brand.contains("honor")
    }
    
    /**
     * 检查是否为HarmonyOS
     */
    fun isHarmonyOS(): Boolean {
        return try {
            val clazz = Class.forName("ohos.system.version.SystemVersion")
            true
        } catch (e: ClassNotFoundException) {
            // 检查系统属性
            val harmonyVersion = getSystemProperty("hw_sc.build.platform.version", "")
            harmonyVersion.isNotEmpty()
        }
    }
    
    /**
     * 检查是否为Kirin处理器
     */
    fun isKirinProcessor(): Boolean {
        val hardware = Build.HARDWARE.lowercase()
        val board = Build.BOARD.lowercase()
        return hardware.contains("kirin") || 
               hardware.contains("hi") ||
               board.contains("kirin") ||
               board.contains("hi")
    }
    
    /**
     * 获取华为设备型号
     */
    fun getHuaweiModel(): String {
        return "${Build.MANUFACTURER} ${Build.MODEL}"
    }
    
    /**
     * 初始化华为设备兼容性设置
     */
    fun initHuaweiCompatibility(context: Context) {
        if (isHuaweiDevice()) {
            Log.i(TAG, "检测到华为设备: ${getHuaweiModel()}")
            Log.i(TAG, "HarmonyOS: ${isHarmonyOS()}")
            Log.i(TAG, "Kirin处理器: ${isKirinProcessor()}")
            Log.i(TAG, "Android版本: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
            
            // 应用华为特定的优化设置
            applyHuaweiOptimizations(context)
        }
    }
    
    /**
     * 应用华为设备优化设置
     */
    private fun applyHuaweiOptimizations(context: Context) {
        try {
            // 这里可以添加华为设备特定的优化代码
            Log.d(TAG, "应用华为设备优化设置")
        } catch (e: Exception) {
            Log.w(TAG, "应用华为优化设置时出错", e)
        }
    }
    
    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String, defaultValue: String): String {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getMethod("get", String::class.java, String::class.java)
            method.invoke(null, key, defaultValue) as String
        } catch (e: Exception) {
            defaultValue
        }
    }
    
    /**
     * 检查设备兼容性
     */
    fun checkCompatibility(): CompatibilityInfo {
        return CompatibilityInfo(
            isHuaweiDevice = isHuaweiDevice(),
            isHarmonyOS = isHarmonyOS(),
            isKirinProcessor = isKirinProcessor(),
            deviceModel = getHuaweiModel(),
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            architecture = Build.SUPPORTED_ABIS.joinToString(", ")
        )
    }
}

/**
 * 设备兼容性信息
 */
data class CompatibilityInfo(
    val isHuaweiDevice: Boolean,
    val isHarmonyOS: Boolean,
    val isKirinProcessor: Boolean,
    val deviceModel: String,
    val androidVersion: String,
    val apiLevel: Int,
    val architecture: String
)
