package operator.mmwave.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import operator.mmwave.data.*
import operator.mmwave.repository.DataRepository

/**
 * 操作员界面 ViewModel
 */
class OperatorViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = DataRepository(application)
    
    // 暴露状态流
    val operatorState: StateFlow<OperatorState> = repository.operatorState
    val statusMessage: StateFlow<String> = repository.statusMessage
    
    /**
     * 更新基本信息
     */
    fun updateBasicInfo(basicInfo: BasicInfo) {
        repository.updateBasicInfo(basicInfo)
    }
    
    /**
     * 更新传感器角度
     */
    fun updateSensorAngles(sensorAngles: SensorAngles) {
        repository.updateSensorAngles(sensorAngles)
    }
    
    /**
     * 更新检查项
     */
    fun updateCheckItem(itemName: String, checkItem: CheckItem) {
        repository.updateCheckItem(itemName, checkItem)
    }
    
    /**
     * 更新毫米波雷达数据
     */
    fun updateMmwaveData(mmwaveData: MmwaveData) {
        repository.updateMmwaveData(mmwaveData)
    }
    
    /**
     * 更新毫米波雷达故障诊断
     */
    fun updateMmwaveFaultDiagnosis(faultDiagnosis: MmwaveFaultDiagnosis) {
        repository.updateMmwaveFaultDiagnosis(faultDiagnosis)
    }
    
    /**
     * 更新激光雷达数据
     */
    fun updateLidarData(lidarData: LidarData) {
        repository.updateLidarData(lidarData)
    }
    
    /**
     * 更新激光雷达故障诊断
     */
    fun updateLidarFaultDiagnosis(faultDiagnosis: LidarFaultDiagnosis) {
        repository.updateLidarFaultDiagnosis(faultDiagnosis)
    }
    
    /**
     * 更新标定数据
     */
    fun updateCalibrationData(calibrationData: CalibrationData) {
        repository.updateCalibrationData(calibrationData)
    }
    
    /**
     * 重置所有数据
     */
    fun resetAll() {
        repository.resetAll()
    }

    /**
     * 启用/禁用定时发送
     */
    fun setBroadcastEnabled(enabled: Boolean) {
        repository.setBroadcastEnabled(enabled)
    }

    /**
     * 检查定时发送是否启用
     */
    fun isBroadcastEnabled(): Boolean {
        return repository.isBroadcastEnabled()
    }
    
    /**
     * 生成工单报告
     */
    fun generateReport(onResult: (Boolean, String) -> Unit) {
        viewModelScope.launch {
            try {
                val success = repository.generateReport()
                if (success) {
                    onResult(true, "工单生成成功")
                } else {
                    onResult(false, "工单生成失败")
                }
            } catch (e: Exception) {
                onResult(false, "工单生成出错: ${e.message}")
            }
        }
    }


    
    override fun onCleared() {
        super.onCleared()
        repository.cleanup()
    }
}
