package operator.mmwave.data

/**
 * 操作员界面完整状态数据类
 */
data class OperatorState(
    val basicInfo: BasicInfo = BasicInfo(),
    val sensorAngles: SensorAngles = SensorAngles(),
    val checkItems: Map<String, CheckItem> = CheckItems.items.mapValues { (name, desc) ->
        CheckItem(name, desc)
    },
    val mmwaveData: MmwaveData = MmwaveData(),
    val mmwaveFaultDiagnosis: MmwaveFaultDiagnosis = MmwaveFaultDiagnosis(),
    val lidarData: LidarData = LidarData(),
    val lidarFaultDiagnosis: LidarFaultDiagnosis = LidarFaultDiagnosis(),
    val calibrationData: CalibrationData = CalibrationData(),
    val progressData: ProgressData = ProgressData()
)

/**
 * 工单报告数据
 */
data class ReportData(
    val type: String = "report",
    val filename: String,
    val content: String,
    val timestamp: String,
    val savePath: String,
    val createDir: Boolean = true
)
