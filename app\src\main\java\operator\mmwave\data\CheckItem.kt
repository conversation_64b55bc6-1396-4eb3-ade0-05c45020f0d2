package operator.mmwave.data

/**
 * 检查项数据类
 */
data class CheckItem(
    val name: String,
    val defaultDescription: String,
    val isChecked: Boolean = false,
    val customDescription: String = "",
    val voltageValue: String = "" // 仅用于电压检查项
)

/**
 * 基础检查项目列表
 */
object CheckItems {
    val items = mapOf(
        "安全帽" to "外观无破损，帽箍、下颌带可调节",
        "警示牌" to "完好，字迹清晰",
        "灭火器" to "在有效期内，压力正常",
        "车辆挡块" to "完好无损，数量足够",
        "检查工具" to "齐全完好",
        "毫米波雷达" to "外观完好，连接稳固",
        "激光雷达" to "外观完好，安装紧固",
        "摄像头" to "外观完好，连接稳固",
        "车辆外观" to "外观完好，无明显损伤",
        "车辆制动液液位" to "液位在正常范围内",
        "车辆冷却液液位" to "液位在正常范围内",
        "绝缘手套" to "无破损，在检验有效期内",
        "绝缘测试仪" to "功能正常，校准有效",
        "万用表" to "功能正常，校准有效",
        "车辆低压蓄电池电压" to "蓄电池电压在正常范围内",
        "车辆左前胎压" to "胎压在正常范围内",
        "车辆左后胎压" to "胎压在正常范围内",
        "车辆右前胎压" to "胎压在正常范围内",
        "车辆右后胎压" to "胎压在正常范围内",
        "车辆上电及仪表灯" to "上电正常，无故障指示",
        "智能驾驶平台" to "数量齐全且安装位置正确",
        "急停开关" to "功能正常"
    )
}
