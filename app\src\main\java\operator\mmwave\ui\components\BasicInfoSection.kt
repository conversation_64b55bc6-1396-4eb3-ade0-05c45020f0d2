package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import operator.mmwave.data.BasicInfo

/**
 * 基本信息输入组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BasicInfoSection(
    basicInfo: BasicInfo,
    onBasicInfoChange: (BasicInfo) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "基本信息",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            // IP地址输入
            OutlinedTextField(
                value = basicInfo.ipAddress,
                onValueChange = { onBasicInfoChange(basicInfo.copy(ipAddress = it)) },
                label = { Text("监控端IP地址") },
                placeholder = { Text("如: *************") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true
            )
            
            // 车辆VIN码
            OutlinedTextField(
                value = basicInfo.vehicleVin,
                onValueChange = { onBasicInfoChange(basicInfo.copy(vehicleVin = it)) },
                label = { Text("车辆VIN码") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // 操作人员
            OutlinedTextField(
                value = basicInfo.operatorName,
                onValueChange = { onBasicInfoChange(basicInfo.copy(operatorName = it)) },
                label = { Text("操作人员") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // 日期
            OutlinedTextField(
                value = basicInfo.date,
                onValueChange = { onBasicInfoChange(basicInfo.copy(date = it)) },
                label = { Text("日期") },
                placeholder = { Text("YYYY.MM.DD") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
        }
    }
}
