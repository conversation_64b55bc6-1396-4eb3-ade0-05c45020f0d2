package operator.mmwave.data

/**
 * 毫米波雷达测量数据
 */
data class MmwaveData(
    // 角反射器坐标
    val reflectorX: String = "",
    val reflectorY: String = "",
    
    // 大陆雷达软件测量值
    val mmwRealId: String = "",
    val mmwDistLat: String = "",
    val mmwDistLong: String = "",
    val mmwVlat: String = "",
    val mmwVlong: String = "",
    
    // USB_CAN_TOOL软件解析数据
    val canRawData: String = "",
    val canRealId: String = "",
    val canDistLat: String = "",
    val canDistLong: String = "",
    val canVlat: String = "",
    val canVlong: String = ""
)

/**
 * 毫米波雷达故障诊断数据
 */
data class MmwaveFaultDiagnosis(
    val faultDescription: String = "",
    val diagnosticSteps: String = "",
    val faultConfirmation: String = "",
    val repairSuggestion: String = ""
)

/**
 * 诊断预设步骤
 */
object DiagnosticPresets {
    val mmwaveNoDataFromHost = listOf(
        "测量CAN分析仪CAN_H电压:",
        "测量CAN分析仪CAN_L电压:",
        "测量毫米波DB9插头的CAN_H:",
        "测量毫米波DB9插头的CAN_L:",
        "使用示波器分析CAN总线波形情况:"
    )
    
    val mmwaveNoDataFromPC = listOf(
        "检查工控机电源状态",
        "检查系统进程是否正常",
        "检查网络连接状态",
        "检查数据接收模块是否正常",
        "检查系统日志是否有报错"
    )
}
