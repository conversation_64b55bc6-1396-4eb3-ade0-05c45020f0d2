Executing tasks: [:app:assembleDebug, :app:assembleDebugUnitTest, :app:assembleDebugAndroidTest] in project E:\AndroidStudioProjects\operator

> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:checkDebugAarMetadata FAILED
> Task :app:mapDebugSourceSetPaths UP-TO-DATE
> Task :app:generateDebugResources UP-TO-DATE
> Task :app:packageDebugResources
> Task :app:createDebugCompatibleScreenManifests
> Task :app:mergeDebugResources
> Task :app:extractDeepLinksDebug
> Task :app:parseDebugLocalResources
> Task :app:processDebugMainManifest
> Task :app:processDebugManifest
> Task :app:processDebugManifestForPackage
> Task :app:javaPreCompileDebug
> Task :app:mergeDebugShaders
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets
> Task :app:compressDebugAssets
> Task :app:desugarDebugFileDependencies
> Task :app:mergeDebugJniLibFolders
> Task :app:checkDebugDuplicateClasses
> Task :app:mergeDebugNativeLibs
> Task :app:validateSigningDebug
> Task :app:writeDebugAppMetadata
> Task :app:mergeLibDexDebug
> Task :app:writeDebugSigningConfigVersions
> Task :app:preDebugUnitTestBuild UP-TO-DATE
> Task :app:preDebugAndroidTestBuild SKIPPED
> Task :app:javaPreCompileDebugUnitTest

> Task :app:stripDebugDebugSymbols
Unable to strip the following libraries, packaging them as they are: libandroidx.graphics.path.so. Run with --info option to learn more.

> Task :app:checkDebugAndroidTestAarMetadata
> Task :app:generateDebugAndroidTestResValues
> Task :app:mapDebugAndroidTestSourceSetPaths
> Task :app:generateDebugAndroidTestResources
> Task :app:processDebugAndroidTestManifest
> Task :app:javaPreCompileDebugAndroidTest
> Task :app:mergeDebugAndroidTestResources
> Task :app:mergeDebugAndroidTestShaders
Download https://dl.google.com/dl/android/maven2/com/android/tools/build/aapt2/8.10.1-12782657/aapt2-8.10.1-12782657.pom, took 319 ms
Download https://dl.google.com/dl/android/maven2/com/android/tools/build/aapt2/8.10.1-12782657/aapt2-8.10.1-12782657-windows.jar, took 764 ms
> Task :app:compileDebugAndroidTestShaders NO-SOURCE
> Task :app:generateDebugAndroidTestAssets UP-TO-DATE
> Task :app:mergeDebugAndroidTestAssets
> Task :app:compressDebugAndroidTestAssets
> Task :app:desugarDebugAndroidTestFileDependencies
> Task :app:checkDebugAndroidTestDuplicateClasses
> Task :app:mergeExtDexDebug
> Task :app:processDebugAndroidTestResources
> Task :app:mergeDebugAndroidTestJniLibFolders
> Task :app:mergeLibDexDebugAndroidTest
> Task :app:mergeDebugAndroidTestNativeLibs NO-SOURCE
> Task :app:stripDebugAndroidTestDebugSymbols NO-SOURCE
> Task :app:validateSigningDebugAndroidTest
> Task :app:writeDebugAndroidTestSigningConfigVersions
> Task :app:mergeExtDexDebugAndroidTest

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:checkDebugAarMetadata'.
> A failure occurred while executing com.android.build.gradle.internal.tasks.CheckAarMetadataWorkAction
   > 7 issues were found when checking AAR metadata:
     
       1.  Dependency 'androidx.core:core:1.16.0' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       2.  Dependency 'androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       3.  Dependency 'androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       4.  Dependency 'androidx.activity:activity:1.10.1' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       5.  Dependency 'androidx.activity:activity-ktx:1.10.1' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       6.  Dependency 'androidx.activity:activity-compose:1.10.1' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).
     
       7.  Dependency 'androidx.core:core-ktx:1.16.0' requires libraries and applications that
           depend on it to compile against version 35 or later of the
           Android APIs.
     
           :app is currently compiled against android-34.
     
           Recommended action: Update this project to use a newer compileSdk
           of at least 35, for example 36.
     
           Note that updating a library or application's compileSdk (which
           allows newer APIs to be used) can be done separately from updating
           targetSdk (which opts the app in to new runtime behavior) and
           minSdk (which determines which devices the app can be installed
           on).

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 20s
45 actionable tasks: 42 executed, 3 up-to-date
