package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import operator.mmwave.data.CalibrationData
import operator.mmwave.data.CameraCalibrationPoint
import operator.mmwave.data.CameraImagePoint

/**
 * 传感器联合标定数据组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalibrationSection(
    calibrationData: CalibrationData,
    onCalibrationDataChange: (CalibrationData) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "传感器联合标定数据",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 摄像头标定点实际坐标值
            Text(
                text = "摄像头标定点实际坐标值（单位：m）",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            calibrationData.cameraPoints.forEachIndexed { index, point ->
                val pointNames = listOf("左上", "右上", "左下", "右下")
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "${pointNames[index]}：",
                        modifier = Modifier.alignByBaseline().width(50.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    OutlinedTextField(
                        value = point.x,
                        onValueChange = { newX ->
                            val newPoints = calibrationData.cameraPoints.toMutableList()
                            newPoints[index] = point.copy(x = newX)
                            onCalibrationDataChange(calibrationData.copy(cameraPoints = newPoints))
                        },
                        label = { Text("X${index + 1}") },
                        modifier = Modifier.weight(1f),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = point.y,
                        onValueChange = { newY ->
                            val newPoints = calibrationData.cameraPoints.toMutableList()
                            newPoints[index] = point.copy(y = newY)
                            onCalibrationDataChange(calibrationData.copy(cameraPoints = newPoints))
                        },
                        label = { Text("Y${index + 1}") },
                        modifier = Modifier.weight(1f),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = point.z,
                        onValueChange = { newZ ->
                            val newPoints = calibrationData.cameraPoints.toMutableList()
                            newPoints[index] = point.copy(z = newZ)
                            onCalibrationDataChange(calibrationData.copy(cameraPoints = newPoints))
                        },
                        label = { Text("Z${index + 1}") },
                        modifier = Modifier.weight(1f),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        singleLine = true
                    )
                }
            }


        }
    }
}
