package operator.mmwave.data

import com.google.gson.annotations.SerializedName

/**
 * 进度数据类
 */
data class ProgressData(
    val baseCheckProgress: Float = 0f,
    val sensorAngleProgress: Float = 0f,
    val mmwaveDataProgress: Float = 0f,
    val mmwaveFaultProgress: Float = 0f,
    val lidarDataProgress: Float = 0f,
    val lidarFaultProgress: Float = 0f,
    val calibrationProgress: Float = 0f,
    val totalProgress: Float = 0f
) {
    companion object {
        // 权重配置
        const val BASE_SENSOR_WEIGHT = 0.10f
        const val MMW_WEIGHT = 0.30f
        const val LIDAR_WEIGHT = 0.30f
        const val CALIBRATION_WEIGHT = 0.30f
    }
}

/**
 * 网络发送数据结构 - 与Python脚本完全一致的字段名
 */
data class NetworkData(
    @SerializedName("base_check")
    val baseCheck: BaseCheckData,
    @SerializedName("total_progress")
    val totalProgress: Float,
    @SerializedName("mmw_test_progress")
    val mmwTestProgress: Float,
    @SerializedName("lidar_measure_progress")
    val lidarMeasureProgress: Float,
    @SerializedName("sensor_fusion_progress")
    val sensorFusionProgress: Float
)

data class BaseCheckData(
    @SerializedName("total_items")
    val totalItems: Int,
    @SerializedName("completed_items")
    val completedItems: Int,
    @SerializedName("checked_items")
    val checkedItems: List<String>
)
