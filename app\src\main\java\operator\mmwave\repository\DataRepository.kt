package operator.mmwave.repository

import android.content.Context
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.Timer
import java.util.TimerTask
import android.os.Environment
import operator.mmwave.data.*
import operator.mmwave.network.UdpClient
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 数据仓库，管理应用状态和网络通信
 */
class DataRepository(private val context: Context) {
    private val udpClient = UdpClient()

    // 应用状态
    private val _operatorState = MutableStateFlow(OperatorState())
    val operatorState: StateFlow<OperatorState> = _operatorState.asStateFlow()

    // 状态标签
    private val _statusMessage = MutableStateFlow("等待发送...")
    val statusMessage: StateFlow<String> = _statusMessage.asStateFlow()

    // 定时发送器 - 复制 Python 脚本的定时发送功能
    private var broadcastTimer: Timer? = null
    
    companion object {
        private const val TAG = "DataRepository"
        private const val DATA_DIR = "data"
        private const val BROADCAST_INTERVAL = 5000L // 5秒间隔，与Python脚本一致
        private const val EXPORT_DIR = "operator" // 导出目录名
    }

    init {
        // 启动定时发送器，复制Python脚本的行为
        startBroadcastTimer()
    }
    
    /**
     * 更新基本信息
     */
    fun updateBasicInfo(basicInfo: BasicInfo) {
        _operatorState.value = _operatorState.value.copy(basicInfo = basicInfo)
        updateProgress()
    }
    
    /**
     * 更新传感器角度
     */
    fun updateSensorAngles(sensorAngles: SensorAngles) {
        _operatorState.value = _operatorState.value.copy(sensorAngles = sensorAngles)
        updateProgress()
    }
    
    /**
     * 更新检查项
     */
    fun updateCheckItem(itemName: String, checkItem: CheckItem) {
        val currentItems = _operatorState.value.checkItems.toMutableMap()
        currentItems[itemName] = checkItem
        _operatorState.value = _operatorState.value.copy(checkItems = currentItems)
        updateProgress()
    }
    
    /**
     * 更新毫米波雷达数据
     */
    fun updateMmwaveData(mmwaveData: MmwaveData) {
        _operatorState.value = _operatorState.value.copy(mmwaveData = mmwaveData)
        updateProgress()
    }
    
    /**
     * 更新毫米波雷达故障诊断
     */
    fun updateMmwaveFaultDiagnosis(faultDiagnosis: MmwaveFaultDiagnosis) {
        _operatorState.value = _operatorState.value.copy(mmwaveFaultDiagnosis = faultDiagnosis)
        updateProgress()
    }
    
    /**
     * 更新激光雷达数据
     */
    fun updateLidarData(lidarData: LidarData) {
        _operatorState.value = _operatorState.value.copy(lidarData = lidarData)
        updateProgress()
    }
    
    /**
     * 更新激光雷达故障诊断
     */
    fun updateLidarFaultDiagnosis(faultDiagnosis: LidarFaultDiagnosis) {
        _operatorState.value = _operatorState.value.copy(lidarFaultDiagnosis = faultDiagnosis)
        updateProgress()
    }
    
    /**
     * 更新标定数据
     */
    fun updateCalibrationData(calibrationData: CalibrationData) {
        _operatorState.value = _operatorState.value.copy(calibrationData = calibrationData)
        updateProgress()
    }
    
    /**
     * 计算并更新进度
     */
    private fun updateProgress() {
        val state = _operatorState.value
        
        // 基础检查项进度 - 与Python程序逻辑一致
        val totalCheckItems = state.checkItems.size
        val completedCheckItems = state.checkItems.values.count { item ->
            var isProcessed = false

            if (item.isChecked) {
                isProcessed = true
            } else {
                // 如果未勾选，但用户输入了不合格说明
                if (item.customDescription.isNotBlank()) {
                    isProcessed = true
                }

                // 对于电压检查项，如果未勾选，但电压输入框有内容，也算作已处理
                if (item.name == "车辆低压蓄电池电压" && item.voltageValue.isNotBlank()) {
                    isProcessed = true
                }
            }

            isProcessed
        }
        val baseCheckProgress = if (totalCheckItems > 0) completedCheckItems.toFloat() / totalCheckItems else 0f
        
        // 传感器角度进度 - 只计算现有的2个项目
        val sensorFields = listOf(
            state.sensorAngles.mmwAngle,
            state.sensorAngles.centerLidarAngle
        )
        val sensorAngleProgress = sensorFields.count { it.isNotBlank() }.toFloat() / sensorFields.size
        
        // 毫米波雷达数据进度 - 删除已移除的字段
        val mmwFields = listOf(
            state.mmwaveData.reflectorX, state.mmwaveData.reflectorY,
            state.mmwaveData.mmwRealId, state.mmwaveData.mmwDistLat, state.mmwaveData.mmwDistLong,
            state.mmwaveData.canRawData, state.mmwaveData.canRealId, state.mmwaveData.canDistLat,
            state.mmwaveData.canDistLong
        )
        val mmwDataProgress = mmwFields.count { it.isNotBlank() }.toFloat() / mmwFields.size
        
        // 毫米波雷达故障诊断进度
        val mmwFaultFields = listOf(
            state.mmwaveFaultDiagnosis.faultDescription,
            state.mmwaveFaultDiagnosis.diagnosticSteps,
            state.mmwaveFaultDiagnosis.faultConfirmation,
            state.mmwaveFaultDiagnosis.repairSuggestion
        )
        val mmwFaultProgress = mmwFaultFields.count { it.isNotBlank() }.toFloat() / mmwFaultFields.size
        
        // 激光雷达数据进度 - 删除已移除的姿态和四元数字段
        val lidarFields = listOf(
            state.lidarData.dummyX, state.lidarData.dummyY, state.lidarData.dummyZ,
            state.lidarData.measureX, state.lidarData.measureY, state.lidarData.measureZ
        )
        val lidarDataProgress = lidarFields.count { it.isNotBlank() }.toFloat() / lidarFields.size
        
        // 激光雷达故障诊断进度
        val lidarFaultFields = listOf(
            state.lidarFaultDiagnosis.faultDescription,
            state.lidarFaultDiagnosis.diagnosticSteps,
            state.lidarFaultDiagnosis.faultConfirmation,
            state.lidarFaultDiagnosis.repairSuggestion
        )
        val lidarFaultProgress = lidarFaultFields.count { it.isNotBlank() }.toFloat() / lidarFaultFields.size
        
        // 标定数据进度
        val calibrationFields = mutableListOf<String>()
        state.calibrationData.cameraPoints.forEach { point ->
            calibrationFields.addAll(listOf(point.x, point.y, point.z))
        }
        state.calibrationData.cameraImagePoints.forEach { point ->
            calibrationFields.addAll(listOf(point.x, point.y))
        }
        calibrationFields.addAll(listOf(
            state.calibrationData.gnssRearCenterLeftRight,
            state.calibrationData.gnssRearCenterHeight,
            state.calibrationData.gnssRearCenterAngle,
            state.calibrationData.mmwLeftRight,
            state.calibrationData.mmwFrontRear,
            state.calibrationData.mmwHeight,
            state.calibrationData.mmwMaxDistance,
            state.calibrationData.lidarLeftRight,
            state.calibrationData.lidarFrontRear,
            state.calibrationData.lidarHeight,
            state.calibrationData.lidarMaxDistance,
            state.calibrationData.mainCameraHeight
        ))
        val calibrationProgress = calibrationFields.count { it.isNotBlank() }.toFloat() / calibrationFields.size
        
        // 计算总进度
        val baseSensorProgress = (baseCheckProgress + sensorAngleProgress) / 2f
        val mmwProgress = mmwDataProgress * 0.6f + mmwFaultProgress * 0.4f
        val lidarProgress = lidarDataProgress * 0.6f + lidarFaultProgress * 0.4f
        
        val totalProgress = baseSensorProgress * ProgressData.BASE_SENSOR_WEIGHT +
                mmwProgress * ProgressData.MMW_WEIGHT +
                lidarProgress * ProgressData.LIDAR_WEIGHT +
                calibrationProgress * ProgressData.CALIBRATION_WEIGHT
        
        val progressData = ProgressData(
            baseCheckProgress = baseCheckProgress,
            sensorAngleProgress = sensorAngleProgress,
            mmwaveDataProgress = mmwDataProgress,
            mmwaveFaultProgress = mmwFaultProgress,
            lidarDataProgress = lidarDataProgress,
            lidarFaultProgress = lidarFaultProgress,
            calibrationProgress = calibrationProgress,
            totalProgress = totalProgress
        )
        
        _operatorState.value = _operatorState.value.copy(progressData = progressData)
        
        // 发送网络数据
        sendNetworkUpdate()
    }
    
    /**
     * 启动定时发送器 - 复制Python脚本的定时发送功能
     */
    private fun startBroadcastTimer() {
        stopBroadcastTimer() // 确保没有重复的定时器

        broadcastTimer = Timer("NetworkBroadcastTimer", true)
        broadcastTimer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                // 在后台线程中发送网络更新
                sendNetworkUpdate()
                Log.d(TAG, "定时发送网络更新 - 每${BROADCAST_INTERVAL}ms")
            }
        }, BROADCAST_INTERVAL, BROADCAST_INTERVAL) // 延迟5秒后开始，然后每5秒执行一次

        Log.d(TAG, "定时发送器已启动，间隔: ${BROADCAST_INTERVAL}ms")
    }

    /**
     * 停止定时发送器
     */
    private fun stopBroadcastTimer() {
        broadcastTimer?.cancel()
        broadcastTimer = null
        Log.d(TAG, "定时发送器已停止")
    }

    /**
     * 发送网络更新
     */
    private fun sendNetworkUpdate() {
        val state = _operatorState.value
        val targetIp = state.basicInfo.ipAddress

        Log.d(TAG, "sendNetworkUpdate called, targetIp: '$targetIp'")

        if (targetIp.isBlank()) {
            _statusMessage.value = "未设置目标IP地址"
            Log.w(TAG, "Target IP is blank, not sending data")
            return
        }
        
        // 计算已完成的项目 - 与进度计算逻辑保持一致
        val completedItems = state.checkItems.values.count { item ->
            var isProcessed = false

            if (item.isChecked) {
                isProcessed = true
            } else {
                // 如果未勾选，但用户输入了不合格说明
                if (item.customDescription.isNotBlank()) {
                    isProcessed = true
                }

                // 对于电压检查项，如果未勾选，但电压输入框有内容，也算作已处理
                if (item.name == "车辆低压蓄电池电压" && item.voltageValue.isNotBlank()) {
                    isProcessed = true
                }
            }

            isProcessed
        }

        // 获取已处理项目的名称列表（用于调试和日志）
        val checkedItems = state.checkItems.filter { (_, item) ->
            item.isChecked || item.customDescription.isNotBlank() ||
            (item.name == "车辆低压蓄电池电压" && item.voltageValue.isNotBlank())
        }.keys.toList()

        val networkData = NetworkData(
            baseCheck = BaseCheckData(
                totalItems = state.checkItems.size,
                completedItems = completedItems,
                checkedItems = checkedItems
            ),
            totalProgress = state.progressData.totalProgress,
            mmwTestProgress = state.progressData.mmwaveDataProgress * 0.6f + state.progressData.mmwaveFaultProgress * 0.4f,
            lidarMeasureProgress = state.progressData.lidarDataProgress * 0.6f + state.progressData.lidarFaultProgress * 0.4f,
            sensorFusionProgress = state.progressData.calibrationProgress
        )

        Log.d(TAG, "Network data prepared:")
        Log.d(TAG, "  - Total items: ${state.checkItems.size}")
        Log.d(TAG, "  - Completed items: $completedItems")
        Log.d(TAG, "  - Checked items: $checkedItems")
        Log.d(TAG, "  - Total progress: ${state.progressData.totalProgress}")
        Log.d(TAG, "  - Target IP: $targetIp")
        
        // 使用协程发送数据
        CoroutineScope(Dispatchers.IO).launch {
            Log.d(TAG, "Starting UDP send operation...")
            val success = udpClient.sendData(networkData, targetIp)
            val message = if (success) "数据发送成功到 $targetIp" else "数据发送失败到 $targetIp"
            _statusMessage.value = message
            Log.d(TAG, "Send result: $message")
        }
    }
    
    /**
     * 重置所有数据
     */
    fun resetAll() {
        _operatorState.value = OperatorState()
        _statusMessage.value = "等待发送..."
    }

    /**
     * 启用/禁用定时发送
     */
    fun setBroadcastEnabled(enabled: Boolean) {
        if (enabled) {
            startBroadcastTimer()
            _statusMessage.value = "定时发送已启用"
        } else {
            stopBroadcastTimer()
            _statusMessage.value = "定时发送已禁用"
        }
        Log.d(TAG, "定时发送状态: ${if (enabled) "启用" else "禁用"}")
    }

    /**
     * 检查定时发送是否启用
     */
    fun isBroadcastEnabled(): Boolean {
        return broadcastTimer != null
    }
    
    /**
     * 生成工单报告
     */
    suspend fun generateReport(): Boolean {
        val state = _operatorState.value
        val dateStr = state.basicInfo.date.ifBlank { 
            SimpleDateFormat("yyyy.MM.dd", Locale.getDefault()).format(Date())
        }
        
        // 生成报告内容
        val report = buildString {
            appendLine("车辆信息：${state.basicInfo.vehicleVin}")
            appendLine("客户信息：${state.basicInfo.operatorName}")
            appendLine("日期：$dateStr")
            appendLine()
            
            appendLine("传感器安装角度：")
            appendLine("毫米波雷达安装角度：${state.sensorAngles.mmwAngle.ifBlank { "未填写" }}")
            appendLine("左侧激光雷达安装角度：${state.sensorAngles.leftLidarAngle.ifBlank { "未填写" }}")
            appendLine("右侧激光雷达安装角度：${state.sensorAngles.rightLidarAngle.ifBlank { "未填写" }}")
            appendLine("中间激光雷达安装角度：${state.sensorAngles.centerLidarAngle.ifBlank { "未填写" }}")
            appendLine("前摄像头安装角度：${state.sensorAngles.frontCameraAngle.ifBlank { "未填写" }}")
            appendLine("左摄像头安装角度：${state.sensorAngles.leftCameraAngle.ifBlank { "未填写" }}")
            appendLine("右摄像头安装角度：${state.sensorAngles.rightCameraAngle.ifBlank { "未填写" }}")
            appendLine()
            
            appendLine("基础检查项：")
            state.checkItems.forEach { (name, item) ->
                val status = if (item.isChecked) "合格" else "不合格"
                val description = item.customDescription.ifBlank { item.defaultDescription }
                appendLine("$name：$status - $description")
            }
            
            // 添加其他数据...
            appendLine()
            appendLine("毫米波雷达装调与测试：")
            appendLine("角反射器坐标：X=${state.mmwaveData.reflectorX}, Y=${state.mmwaveData.reflectorY}")
            // ... 更多内容
        }
        
        // 保存到外部存储目录
        val filename = "$dateStr-工单.txt"
        val success = saveReportToExternalStorage(filename, report)

        if (success) {
            // 发送到监控端
            val reportData = ReportData(
                filename = filename,
                content = report,
                timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()),
                savePath = "C:/MonitorSystem/reports"
            )

            val targetIp = state.basicInfo.ipAddress
            if (targetIp.isNotBlank()) {
                udpClient.sendData(reportData, targetIp)
            }
        }

        return success
    }
    
    /**
     * 保存报告到外部存储
     */
    private fun saveReportToExternalStorage(filename: String, content: String): Boolean {
        return try {
            // 检查存储权限
            if (!hasStoragePermission()) {
                Log.w(TAG, "No storage permission, cannot save report to external storage")
                return false
            }

            // 创建导出目录 /storage/emulated/0/operator/
            val exportDir = File(Environment.getExternalStorageDirectory(), EXPORT_DIR)
            if (!exportDir.exists()) {
                val created = exportDir.mkdirs()
                if (!created) {
                    Log.e(TAG, "Failed to create export directory: ${exportDir.absolutePath}")
                    return false
                }
                Log.d(TAG, "Created export directory: ${exportDir.absolutePath}")
            }

            // 保存工单文件
            val file = File(exportDir, filename)
            FileWriter(file).use { writer ->
                writer.write(content)
            }

            Log.d(TAG, "Report saved to external storage: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save report to external storage", e)
            false
        }
    }
    
    /**
     * 导出工单到外部存储
     */
    suspend fun exportReportToExternalStorage(filename: String): Pair<Boolean, String> {
        return try {
            // 检查是否有存储权限
            if (!hasStoragePermission()) {
                return Pair(false, "没有存储权限，请在设置中授予存储权限")
            }

            // 创建导出目录 /storage/emulated/0/operator/
            val exportDir = File(Environment.getExternalStorageDirectory(), EXPORT_DIR)
            if (!exportDir.exists()) {
                val created = exportDir.mkdirs()
                if (!created) {
                    return Pair(false, "无法创建导出目录: ${exportDir.absolutePath}")
                }
                Log.d(TAG, "Created export directory: ${exportDir.absolutePath}")
            }

            // 从应用私有目录读取工单文件
            val privateFile = File(File(context.filesDir, DATA_DIR), filename)
            if (!privateFile.exists()) {
                return Pair(false, "工单文件不存在: $filename")
            }

            // 复制到外部存储
            val exportFile = File(exportDir, filename)
            privateFile.copyTo(exportFile, overwrite = true)

            val exportPath = exportFile.absolutePath
            Log.d(TAG, "Report exported to: $exportPath")

            Pair(true, "工单已导出到: $exportPath")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to export report", e)
            Pair(false, "导出失败: ${e.message}")
        }
    }

    /**
     * 导出所有工单到外部存储
     */
    suspend fun exportAllReportsToExternalStorage(): Pair<Boolean, String> {
        return try {
            // 检查是否有存储权限
            if (!hasStoragePermission()) {
                return Pair(false, "没有存储权限，请在设置中授予存储权限")
            }

            // 创建导出目录
            val exportDir = File(Environment.getExternalStorageDirectory(), EXPORT_DIR)
            if (!exportDir.exists()) {
                val created = exportDir.mkdirs()
                if (!created) {
                    return Pair(false, "无法创建导出目录: ${exportDir.absolutePath}")
                }
            }

            // 获取所有工单文件
            val privateDir = File(context.filesDir, DATA_DIR)
            val reportFiles = privateDir.listFiles { file ->
                file.isFile && file.name.endsWith("-工单.txt")
            } ?: emptyArray()

            if (reportFiles.isEmpty()) {
                return Pair(false, "没有找到工单文件")
            }

            // 复制所有工单文件
            var successCount = 0
            for (file in reportFiles) {
                try {
                    val exportFile = File(exportDir, file.name)
                    file.copyTo(exportFile, overwrite = true)
                    successCount++
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to export file: ${file.name}", e)
                }
            }

            val exportPath = exportDir.absolutePath
            Log.d(TAG, "Exported $successCount/${reportFiles.size} reports to: $exportPath")

            Pair(true, "已导出 $successCount 个工单到: $exportPath")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to export all reports", e)
            Pair(false, "批量导出失败: ${e.message}")
        }
    }

    /**
     * 检查存储权限
     */
    private fun hasStoragePermission(): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            // Android 11+ 使用 MANAGE_EXTERNAL_STORAGE
            Environment.isExternalStorageManager()
        } else {
            // Android 10 及以下使用传统权限
            context.checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 获取导出目录路径
     */
    fun getExportDirectoryPath(): String {
        return File(Environment.getExternalStorageDirectory(), EXPORT_DIR).absolutePath
    }

    /**
     * 获取所有本地工单文件列表
     */
    fun getLocalReportFiles(): List<String> {
        return try {
            val privateDir = File(context.filesDir, DATA_DIR)
            val reportFiles = privateDir.listFiles { file ->
                file.isFile && file.name.endsWith("-工单.txt")
            } ?: emptyArray()

            reportFiles.map { it.name }.sorted().reversed() // 按日期倒序
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get local report files", e)
            emptyList()
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopBroadcastTimer() // 停止定时发送器
        udpClient.close()
        Log.d(TAG, "DataRepository 资源已清理")
    }
}
