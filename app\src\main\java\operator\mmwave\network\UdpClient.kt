package operator.mmwave.network

import android.util.Log
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.net.InetAddress
import java.net.SocketException

/**
 * UDP 客户端，用于向监控端发送数据
 */
class UdpClient {
    private var socket: DatagramSocket? = null
    private val gson = Gson()
    
    companion object {
        private const val TAG = "UdpClient"
        private const val TARGET_PORT = 45454
        private const val SOCKET_TIMEOUT = 5000 // 5秒超时
    }
    
    /**
     * 初始化UDP客户端
     */
    private fun initSocket() {
        try {
            if (socket?.isClosed != false) {
                socket = DatagramSocket()
                socket?.soTimeout = SOCKET_TIMEOUT
                Log.d(TAG, "UDP Socket initialized")
            }
        } catch (e: SocketException) {
            Log.e(TAG, "Failed to initialize UDP socket", e)
        }
    }
    
    /**
     * 发送数据到指定IP地址
     */
    suspend fun sendData(data: Any, targetIp: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // 验证IP地址格式
            if (!isValidIpAddress(targetIp)) {
                Log.e(TAG, "Invalid IP address format: $targetIp")
                return@withContext false
            }
            
            // 初始化socket
            initSocket()
            
            // 将数据转换为JSON
            val jsonData = gson.toJson(data)
            val messageBytes = jsonData.toByteArray(Charsets.UTF_8)

            Log.d(TAG, "Preparing to send data to $targetIp, size: ${messageBytes.size} bytes")
            Log.d(TAG, "JSON data: $jsonData")
            
            // 创建数据包
            val address = InetAddress.getByName(targetIp)
            val packet = DatagramPacket(
                messageBytes,
                messageBytes.size,
                address,
                TARGET_PORT
            )
            
            // 发送数据
            socket?.send(packet)
            Log.d(TAG, "Data sent successfully to $targetIp:$TARGET_PORT")
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send data to $targetIp", e)
            false
        }
    }
    
    /**
     * 验证IP地址格式
     */
    private fun isValidIpAddress(ip: String): Boolean {
        val parts = ip.split(".")
        if (parts.size != 4) return false
        
        return parts.all { part ->
            try {
                val num = part.toInt()
                num in 0..255
            } catch (e: NumberFormatException) {
                false
            }
        }
    }
    
    /**
     * 关闭UDP客户端
     */
    fun close() {
        try {
            socket?.close()
            Log.d(TAG, "UDP Socket closed")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing UDP socket", e)
        }
    }
}
