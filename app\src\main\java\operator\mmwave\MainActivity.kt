package operator.mmwave

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Create
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.List
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.activity.result.contract.ActivityResultContracts
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import java.io.File
import android.os.Environment
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import operator.mmwave.ui.components.*
import operator.mmwave.ui.theme.OperatorTheme
import operator.mmwave.viewmodel.OperatorViewModel
import operator.mmwave.utils.HuaweiCompatibility

class MainActivity : ComponentActivity() {

    // 权限申请启动器
    private lateinit var storagePermissionLauncher: ActivityResultLauncher<String>
    private lateinit var manageStorageLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化华为设备兼容性
        HuaweiCompatibility.initHuaweiCompatibility(this)

        // 初始化权限申请启动器
        initPermissionLaunchers()

        // 检查并申请存储权限
        checkAndRequestStoragePermission()

        enableEdgeToEdge()
        setContent {
            OperatorTheme {
                OperatorScreen()
            }
        }
    }

    /**
     * 初始化权限申请启动器
     */
    private fun initPermissionLaunchers() {
        // 传统存储权限申请
        storagePermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                android.util.Log.d("MainActivity", "Storage permission granted")
            } else {
                android.util.Log.w("MainActivity", "Storage permission denied")
                // 可以显示权限说明对话框
            }
        }

        // Android 11+ 管理所有文件权限申请
        manageStorageLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (android.os.Environment.isExternalStorageManager()) {
                    android.util.Log.d("MainActivity", "Manage external storage permission granted")
                } else {
                    android.util.Log.w("MainActivity", "Manage external storage permission denied")
                }
            }
        }
    }

    /**
     * 检查并申请存储权限
     */
    private fun checkAndRequestStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 需要 MANAGE_EXTERNAL_STORAGE 权限
            if (!android.os.Environment.isExternalStorageManager()) {
                android.util.Log.d("MainActivity", "Requesting MANAGE_EXTERNAL_STORAGE permission")
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                    intent.data = Uri.parse("package:$packageName")
                    manageStorageLauncher.launch(intent)
                } catch (e: Exception) {
                    android.util.Log.e("MainActivity", "Failed to request manage storage permission", e)
                    // 降级到通用设置页面
                    val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                    manageStorageLauncher.launch(intent)
                }
            }
        } else {
            // Android 10 及以下使用传统权限
            if (checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                android.util.Log.d("MainActivity", "Requesting WRITE_EXTERNAL_STORAGE permission")
                storagePermissionLauncher.launch(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OperatorScreen(
    viewModel: OperatorViewModel = viewModel()
) {
    val operatorState by viewModel.operatorState.collectAsStateWithLifecycle()
    val statusMessage by viewModel.statusMessage.collectAsStateWithLifecycle()
    val context = LocalContext.current

    var showReportDialog by remember { mutableStateOf(false) }
    var reportMessage by remember { mutableStateOf("") }
    var reportSuccess by remember { mutableStateOf(false) }

    // 工单列表对话框状态
    var showReportListDialog by remember { mutableStateOf(false) }



    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("操作员界面") },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        bottomBar = {
            BottomAppBar(
                containerColor = MaterialTheme.colorScheme.surface
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 重置按钮
                    OutlinedButton(
                        onClick = { viewModel.resetAll() },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重置所有选项")
                    }

                    // 生成工单按钮 (居中)
                    Button(
                        onClick = {
                            viewModel.generateReport { success, message ->
                                reportSuccess = success
                                reportMessage = message
                                showReportDialog = true
                            }
                        }
                    ) {
                        Icon(Icons.Default.Create, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("生成工单")
                    }

                    // 查看工单按钮
                    OutlinedButton(
                        onClick = { showReportListDialog = true },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Icon(Icons.Default.List, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("查看工单")
                    }
                }
            }
        }
    ) { paddingValues ->
        val listState = rememberLazyListState()

        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 基本信息
            item {
                BasicInfoSection(
                    basicInfo = operatorState.basicInfo,
                    onBasicInfoChange = viewModel::updateBasicInfo
                )
            }

            // 检查项列表
            item {
                CheckItemsSection(
                    checkItems = operatorState.checkItems,
                    onCheckItemChange = viewModel::updateCheckItem
                )
            }

            // 传感器角度设置
            item {
                SensorAnglesSection(
                    sensorAngles = operatorState.sensorAngles,
                    onSensorAnglesChange = viewModel::updateSensorAngles
                )
            }

            // 毫米波雷达测试
            item {
                MmwaveSection(
                    mmwaveData = operatorState.mmwaveData,
                    mmwaveFaultDiagnosis = operatorState.mmwaveFaultDiagnosis,
                    onMmwaveDataChange = viewModel::updateMmwaveData,
                    onMmwaveFaultDiagnosisChange = viewModel::updateMmwaveFaultDiagnosis
                )
            }

            // 激光雷达测试
            item {
                LidarSection(
                    lidarData = operatorState.lidarData,
                    lidarFaultDiagnosis = operatorState.lidarFaultDiagnosis,
                    onLidarDataChange = viewModel::updateLidarData,
                    onLidarFaultDiagnosisChange = viewModel::updateLidarFaultDiagnosis
                )
            }

            // 传感器联合标定
            item {
                CalibrationSection(
                    calibrationData = operatorState.calibrationData,
                    onCalibrationDataChange = viewModel::updateCalibrationData
                )
            }

            // 进度显示
            item {
                ProgressSection(
                    progressData = operatorState.progressData,
                    statusMessage = statusMessage
                )
            }
        }
    }

    // 工单生成结果对话框
    if (showReportDialog) {
        AlertDialog(
            onDismissRequest = { showReportDialog = false },
            title = {
                Text(if (reportSuccess) "成功" else "错误")
            },
            text = {
                Text(reportMessage)
            },
            confirmButton = {
                TextButton(
                    onClick = { showReportDialog = false }
                ) {
                    Text("确定")
                }
            }
        )
    }

    // 工单列表对话框
    if (showReportListDialog) {
        ReportListDialog(
            onDismiss = { showReportListDialog = false }
        )
    }


}

/**
 * 工单列表对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportListDialog(
    onDismiss: () -> Unit
) {
    val context = LocalContext.current

    // 获取工单文件列表并按月归类
    val reportFilesByMonth = remember {
        try {
            val exportDir = File(Environment.getExternalStorageDirectory(), "operator")
            if (exportDir.exists()) {
                val files = exportDir.listFiles { file ->
                    file.isFile && file.name.endsWith("-工单.txt")
                }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()

                // 按月份归类
                files.groupBy { file ->
                    // 从文件名中提取日期 (YYYY.MM.DD-工单.txt)
                    val fileName = file.name
                    val datePattern = Regex("""(\d{4})\.(\d{2})\.(\d{2})-工单\.txt""")
                    val matchResult = datePattern.find(fileName)
                    if (matchResult != null) {
                        val year = matchResult.groupValues[1]
                        val month = matchResult.groupValues[2]
                        "${year}年${month}月"
                    } else {
                        "其他"
                    }
                }.toSortedMap(compareByDescending { it })
            } else {
                emptyMap()
            }
        } catch (e: Exception) {
            emptyMap()
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("工单列表")
        },
        text = {
            if (reportFilesByMonth.isEmpty()) {
                Text("暂无工单文件\n请先生成工单")
            } else {
                LazyColumn(
                    modifier = Modifier.height(400.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    reportFilesByMonth.forEach { (monthYear, files) ->
                        // 月份标题
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer
                                )
                            ) {
                                Text(
                                    text = "$monthYear (${files.size}个工单)",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    modifier = Modifier.padding(12.dp)
                                )
                            }
                        }

                        // 该月份的工单文件
                        items(files) { file ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp),
                                onClick = {
                                    // 打开文件选择器
                                    openFileWithChooser(context, file)
                                    onDismiss()
                                },
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surface
                                )
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp)
                                ) {
                                    Text(
                                        text = file.name.replace("-工单.txt", ""),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "修改时间: ${
                                            java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
                                                .format(java.util.Date(file.lastModified()))
                                        }",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    Text(
                                        text = "大小: ${String.format("%.1f KB", file.length() / 1024.0)}",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    )
}

/**
 * 使用系统文件选择器打开文件
 */
private fun openFileWithChooser(context: android.content.Context, file: File) {
    try {
        val uri = androidx.core.content.FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, "text/plain")
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        val chooser = Intent.createChooser(intent, "选择打开方式")
        context.startActivity(chooser)
    } catch (e: Exception) {
        android.util.Log.e("MainActivity", "Failed to open file", e)
        // 降级到直接打开文件管理器
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(Uri.fromFile(file.parentFile), "resource/folder")
            }
            context.startActivity(intent)
        } catch (e2: Exception) {
            android.util.Log.e("MainActivity", "Failed to open file manager", e2)
        }
    }
}