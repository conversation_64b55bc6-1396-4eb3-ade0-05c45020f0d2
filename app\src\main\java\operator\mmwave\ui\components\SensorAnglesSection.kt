package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import operator.mmwave.data.SensorAngles

/**
 * 传感器角度设置组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SensorAnglesSection(
    sensorAngles: SensorAngles,
    onSensorAnglesChange: (SensorAngles) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "传感器安装角度",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 毫米波雷达
            OutlinedTextField(
                value = sensorAngles.mmwAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(mmwAngle = it)) },
                label = { Text("毫米波雷达垂直角度值（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
            
            // 激光雷达
            Text(
                text = "激光雷达",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            OutlinedTextField(
                value = sensorAngles.leftLidarAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(leftLidarAngle = it)) },
                label = { Text("右侧激光雷达俯仰角度值（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )

            OutlinedTextField(
                value = sensorAngles.rightLidarAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(rightLidarAngle = it)) },
                label = { Text("左侧激光雷达俯仰角度值（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )

            OutlinedTextField(
                value = sensorAngles.centerLidarAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(centerLidarAngle = it)) },
                label = { Text("中间激光雷达水平角度值（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
            
            // 摄像头
            Text(
                text = "摄像头",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            OutlinedTextField(
                value = sensorAngles.frontCameraAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(frontCameraAngle = it)) },
                label = { Text("前鱼眼摄像头俯仰角（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )

            OutlinedTextField(
                value = sensorAngles.leftCameraAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(leftCameraAngle = it)) },
                label = { Text("右鱼眼摄像头俯仰角（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )

            OutlinedTextField(
                value = sensorAngles.rightCameraAngle,
                onValueChange = { onSensorAnglesChange(sensorAngles.copy(rightCameraAngle = it)) },
                label = { Text("左鱼眼摄像头俯仰角（度）") },
                placeholder = { Text("请输入角度值（度）") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
        }
    }
}
