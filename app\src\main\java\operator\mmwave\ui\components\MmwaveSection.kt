package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import operator.mmwave.data.MmwaveData
import operator.mmwave.data.MmwaveFaultDiagnosis

/**
 * 毫米波雷达测试组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MmwaveSection(
    mmwaveData: MmwaveData,
    mmwaveFaultDiagnosis: MmwaveFaultDiagnosis,
    onMmwaveDataChange: (MmwaveData) -> Unit,
    onMmwaveFaultDiagnosisChange: (MmwaveFaultDiagnosis) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "毫米波雷达装调与测试",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 角反射器距车前实际坐标
            Text(
                text = "角反射器距车前实际坐标",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = mmwaveData.reflectorX,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(reflectorX = it)) },
                    label = { Text("X (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = mmwaveData.reflectorY,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(reflectorY = it)) },
                    label = { Text("Y (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            // 大陆雷达软件测量角反射器信息
            Text(
                text = "大陆雷达软件测量角反射器信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            OutlinedTextField(
                value = mmwaveData.mmwRealId,
                onValueChange = { onMmwaveDataChange(mmwaveData.copy(mmwRealId = it)) },
                label = { Text("障碍物真实 ID") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = mmwaveData.mmwDistLat,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(mmwDistLat = it)) },
                    label = { Text("DistLat (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = mmwaveData.mmwDistLong,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(mmwDistLong = it)) },
                    label = { Text("DistLong (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = mmwaveData.mmwVlat,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(mmwVlat = it)) },
                    label = { Text("Vlat (m/s)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = mmwaveData.mmwVlong,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(mmwVlong = it)) },
                    label = { Text("Vlong (m/s)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            // USB_CAN_TOOL软件解析数据
            Text(
                text = "USB_CAN_TOOL软件解析后值",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            OutlinedTextField(
                value = mmwaveData.canRawData,
                onValueChange = { onMmwaveDataChange(mmwaveData.copy(canRawData = it)) },
                label = { Text("帧原始数据") },
                placeholder = { Text("请输入帧原始数据...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 5
            )
            
            OutlinedTextField(
                value = mmwaveData.canRealId,
                onValueChange = { onMmwaveDataChange(mmwaveData.copy(canRealId = it)) },
                label = { Text("障碍物真实 ID") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = mmwaveData.canDistLat,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(canDistLat = it)) },
                    label = { Text("DistLat (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = mmwaveData.canDistLong,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(canDistLong = it)) },
                    label = { Text("DistLong (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = mmwaveData.canVlat,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(canVlat = it)) },
                    label = { Text("Vlat (m/s)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = mmwaveData.canVlong,
                    onValueChange = { onMmwaveDataChange(mmwaveData.copy(canVlong = it)) },
                    label = { Text("Vlong (m/s)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            // 故障诊断过程
            Text(
                text = "故障诊断过程",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp)
            )
            
            OutlinedTextField(
                value = mmwaveFaultDiagnosis.faultDescription,
                onValueChange = { onMmwaveFaultDiagnosisChange(mmwaveFaultDiagnosis.copy(faultDescription = it)) },
                label = { Text("故障现象描述") },
                placeholder = { Text("请输入故障现象描述...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
            
            OutlinedTextField(
                value = mmwaveFaultDiagnosis.diagnosticSteps,
                onValueChange = { onMmwaveFaultDiagnosisChange(mmwaveFaultDiagnosis.copy(diagnosticSteps = it)) },
                label = { Text("记录诊断过程测量数据并分析（记录关键步骤）") },
                placeholder = { Text("请输入诊断过程测量数据并分析...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 6
            )
            
            OutlinedTextField(
                value = mmwaveFaultDiagnosis.faultConfirmation,
                onValueChange = { onMmwaveFaultDiagnosisChange(mmwaveFaultDiagnosis.copy(faultConfirmation = it)) },
                label = { Text("故障确认") },
                placeholder = { Text("请输入故障确认内容...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
            
            OutlinedTextField(
                value = mmwaveFaultDiagnosis.repairSuggestion,
                onValueChange = { onMmwaveFaultDiagnosisChange(mmwaveFaultDiagnosis.copy(repairSuggestion = it)) },
                label = { Text("故障机理分析及维修建议") },
                placeholder = { Text("请输入故障机理分析及维修建议...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
        }
    }
}
