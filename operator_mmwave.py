import sys
import json
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QCheckBox, QLabel, QPushButton, QGroupBox,
                            QLineEdit, QTextEdit, QComboBox, QFormLayout, QMessageBox,
                            QScrollArea, QSizePolicy, QProgressBar)
from PyQt5.QtCore import QTimer, Qt, QSize, QCoreApplication
from PyQt5.QtNetwork import QUdpSocket, QHostAddress


class OperatorWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('操作员界面')
        self.setGeometry(100, 100, 1000, 900)  # 增大窗口尺寸
        
        # 创建data文件夹（如果不存在）
        self.data_dir = 'data'
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 监控端根目录配置
        self.monitor_root_dir = "C:/MonitorSystem"  # 监控端的根目录
        
        # 创建主滚动区域
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        self.setCentralWidget(main_scroll)
        
        # 创建主内容容器
        content_widget = QWidget()
        main_scroll.setWidget(content_widget)
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                margin-top: 1ex;
                padding-top: 15px;
                padding-bottom: 15px;
                border: 1px solid #ccc;
                border-radius: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
            QLabel {
                font-size: 12pt;
                margin: 0px;
                padding: 0px;
                color: #34495e;
            }
            QLineEdit, QTextEdit, QComboBox {
                font-size: 12pt;
                padding: 8px;
                margin: 0px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QPushButton {
                font-size: 12pt;
                padding: 10px 20px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QCheckBox {
                font-size: 12pt;
                spacing: 8px;
                margin: 0px;
                padding: 0px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QFormLayout {
                margin: 0px;
                padding: 0px;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #ecf0f1;
                width: 14px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #bdc3c7;
                min-height: 30px;
                border-radius: 7px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 添加IP地址输入
        ip_group = QGroupBox('网络设置')
        ip_layout = QVBoxLayout()
        ip_layout.setSpacing(10)
        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText('输入监控端IP地址（如: *************）')
        ip_layout.addWidget(self.ip_input)
        ip_group.setLayout(ip_layout)
        main_layout.addWidget(ip_group)

        # 基本信息区域
        basic_group = QGroupBox('基本信息')
        basic_layout = QFormLayout()
        basic_layout.setSpacing(12)
        basic_layout.setContentsMargins(15, 20, 15, 15)
        
        self.vehicle_info = QLineEdit()
        self.customer_info = QLineEdit()
        
        self.date = QLineEdit()
        # 设置默认日期为今天
        current_date = datetime.now().strftime('%Y.%m.%d')
        self.date.setText(current_date)
        
        basic_layout.addRow('车辆VIN码:', self.vehicle_info)
        basic_layout.addRow('操作人员:', self.customer_info)
       
        basic_layout.addRow('日期:', self.date)
        
        basic_group.setLayout(basic_layout)
        main_layout.addWidget(basic_group)

        # 创建检查项组
        check_group = QGroupBox('基础检查项')
        check_layout = QVBoxLayout()
        check_layout.setSpacing(8)
        check_layout.setContentsMargins(15, 20, 15, 15)
        
        # 创建检查项
        self.check_items = {
            "安全帽": "外观无破损，帽箍、下颌带可调节",
            "警示牌": "完好，字迹清晰",
            "灭火器": "在有效期内，压力正常",
            "车辆挡块": "完好无损，数量足够",
            "检查工具": "齐全完好",   
            "毫米波雷达": "外观完好，连接稳固",
            "激光雷达": "外观完好，安装紧固",
            "摄像头": "外观完好，连接稳固",     
            "车辆外观": "外观完好，无明显损伤",
            "车辆制动液液位": "液位在正常范围内",
            "车辆冷却液液位": "液位在正常范围内",            
            "绝缘手套": "无破损，在检验有效期内",
            "绝缘测试仪": "功能正常，校准有效",            
            "万用表": "功能正常，校准有效",
            "车辆低压蓄电池电压": "蓄电池电压在正常范围内",
            "车辆左前胎压": "胎压在正常范围内",
            "车辆左后胎压": "胎压在正常范围内",
            "车辆右前胎压": "胎压在正常范围内",
            "车辆右后胎压": "胎压在正常范围内",                    
            "车辆上电及仪表灯": "上电正常，无故障指示",
            "智能驾驶平台": "数量齐全且安装位置正确",
            "急停开关": "功能正常"
        }
        
        # 创建检查项控件
        self.check_widgets = {}
        for item, default_text in self.check_items.items():
            item_widget = QWidget()
            item_layout = QHBoxLayout()
            item_layout.setContentsMargins(0, 5, 0, 5)
            
            # 创建勾选框
            checkbox = QCheckBox(item)
            checkbox.setStyleSheet("font-weight: bold;")
            
            # 创建输入框
            text_input = QLineEdit()
            text_input.setPlaceholderText("如不合格，请在此输入具体情况")
            text_input.setEnabled(False)  # 初始状态禁用
            
            # 为蓄电池电压项添加专门的输入框
            voltage_input = None
            if item == "车辆低压蓄电池电压":
                voltage_input = QLineEdit()
                voltage_input.setPlaceholderText("请输入电压值(V)")
                voltage_input.setMaximumWidth(150)  # 限制输入框宽度
                
            checkbox.stateChanged.connect(lambda state, item=item: self.on_check_changed(state, item))
            # 连接文本输入框的textChanged信号到update_progress，以便手动输入时也能更新进度
            text_input.textChanged.connect(self.update_progress)
            if voltage_input:
                voltage_input.textChanged.connect(self.update_progress)
            
            item_layout.addWidget(checkbox, 1)
            if voltage_input:
                item_layout.addWidget(voltage_input)
            item_layout.addWidget(text_input, 3)
            item_widget.setLayout(item_layout)
            
            if voltage_input:
                self.check_widgets[item] = (checkbox, text_input, voltage_input)
            else:
                self.check_widgets[item] = (checkbox, text_input)
            
            check_layout.addWidget(item_widget)
        
        check_group.setLayout(check_layout)
        main_layout.addWidget(check_group)

        # 添加传感器角度设置区域
        sensor_angle_group = QGroupBox('传感器安装角度')
        sensor_angle_layout = QFormLayout()
        sensor_angle_layout.setSpacing(12)
        sensor_angle_layout.setContentsMargins(15, 20, 15, 15)
        
        # 毫米波雷达
        self.mmw_angle = QLineEdit()
        self.mmw_angle.setPlaceholderText('请输入角度值（度）')
        sensor_angle_layout.addRow('毫米波雷达:', self.mmw_angle)
        
        # 左右激光雷达
        self.left_lidar_angle = QLineEdit()
        self.left_lidar_angle.setPlaceholderText('请输入角度值（度）')
        self.right_lidar_angle = QLineEdit()
        self.right_lidar_angle.setPlaceholderText('请输入角度值（度）')
        sensor_angle_layout.addRow('左侧激光雷达:', self.left_lidar_angle)
        sensor_angle_layout.addRow('右侧激光雷达:', self.right_lidar_angle)
        
        # 中间激光雷达
        self.center_lidar_angle = QLineEdit()
        self.center_lidar_angle.setPlaceholderText('请输入角度值（度）')
        sensor_angle_layout.addRow('中间激光雷达:', self.center_lidar_angle)
        
        # 四个摄像头
        self.front_camera_angle = QLineEdit()
        self.front_camera_angle.setPlaceholderText('请输入角度值（度）')
        self.left_camera_angle = QLineEdit()
        self.left_camera_angle.setPlaceholderText('请输入角度值（度）')
        self.right_camera_angle = QLineEdit()
        self.right_camera_angle.setPlaceholderText('请输入角度值（度）')
        
        sensor_angle_layout.addRow('前摄像头:', self.front_camera_angle)
        sensor_angle_layout.addRow('左摄像头:', self.left_camera_angle)
        sensor_angle_layout.addRow('右摄像头:', self.right_camera_angle)
        
        sensor_angle_group.setLayout(sensor_angle_layout)
        main_layout.addWidget(sensor_angle_group)

        # 毫米波雷达测量数据组
        mmw_group = QGroupBox('毫米波雷达装调与测试')
        mmw_layout = QFormLayout()
        mmw_layout.setSpacing(10)
        mmw_layout.setContentsMargins(15, 20, 15, 15)
        
        # 角反射器坐标
        self.reflector_x = QLineEdit()
        self.reflector_y = QLineEdit()
        mmw_layout.addRow('角反射器坐标 X (m):', self.reflector_x)
        mmw_layout.addRow('角反射器坐标 Y (m):', self.reflector_y)
        
        # 大陆雷达软件测量值
        mmw_layout.addRow(QLabel('<b>大陆雷达软件测量值（角反）:</b>'))
        self.mmw_real_id = QLineEdit()
        self.mmw_dist_lat = QLineEdit()
        self.mmw_dist_long = QLineEdit()
        self.mmw_vlat = QLineEdit()
        self.mmw_vlong = QLineEdit()
        mmw_layout.addRow('障碍物真实 ID:', self.mmw_real_id)
        mmw_layout.addRow('DistLat (m):', self.mmw_dist_lat)
        mmw_layout.addRow('DistLong (m):', self.mmw_dist_long)
        mmw_layout.addRow('Vlat (m/s):', self.mmw_vlat)
        mmw_layout.addRow('Vlong (m/s):', self.mmw_vlong)
        
        # USB_CAN_TOOL软件解析数据
        mmw_layout.addRow(QLabel('<b>USB_CAN_TOOL软件解析后值:</b>'))
        self.can_raw_data = QTextEdit()
        self.can_raw_data.setPlaceholderText("请输入帧原始数据...")
        self.can_raw_data.setMinimumHeight(80)
        mmw_layout.addRow('帧原始数据:', self.can_raw_data)
        
        self.can_real_id = QLineEdit()
        self.can_dist_lat = QLineEdit()
        self.can_dist_long = QLineEdit()
        self.can_vlat = QLineEdit()
        self.can_vlong = QLineEdit()
        mmw_layout.addRow('障碍物真实 ID:', self.can_real_id)
        mmw_layout.addRow('DistLat (m):', self.can_dist_lat)
        mmw_layout.addRow('DistLong (m):', self.can_dist_long)
        mmw_layout.addRow('Vlat (m/s):', self.can_vlat)
        mmw_layout.addRow('Vlong (m/s):', self.can_vlong)
        
        mmw_group.setLayout(mmw_layout)
        main_layout.addWidget(mmw_group)

        # 毫米波雷达装调与测试 故障诊断过程
        self.mmw_fault_group = QGroupBox('故障诊断过程')
        mmw_fault_layout = QFormLayout()
        self.mmw_fault_desc = QTextEdit(); self.mmw_fault_desc.setPlaceholderText('请输入故障现象描述...')
        self.mmw_fault_steps = QTextEdit(); self.mmw_fault_steps.setPlaceholderText('请输入诊断过程测量数据并分析...')
        self.mmw_fault_confirm = QTextEdit(); self.mmw_fault_confirm.setPlaceholderText('请输入故障确认内容...')
        self.mmw_fault_suggest = QTextEdit(); self.mmw_fault_suggest.setPlaceholderText('请输入故障机理分析及维修建议...')
        mmw_fault_layout.addRow('故障现象描述:', self.mmw_fault_desc)
        mmw_fault_layout.addRow('记录诊断过程测量数据并分析（记录关键步骤）:', self.mmw_fault_steps)
        mmw_fault_layout.addRow('故障确认:', self.mmw_fault_confirm)
        mmw_fault_layout.addRow('故障机理分析及维修建议:', self.mmw_fault_suggest)
        self.mmw_fault_group.setLayout(mmw_fault_layout)
        main_layout.addWidget(self.mmw_fault_group)
        
        # 激光雷达测量数据组
        lidar_group = QGroupBox('激光雷达装调与测试')
        lidar_layout = QFormLayout()
        lidar_layout.setSpacing(10)
        lidar_layout.setContentsMargins(15, 20, 15, 15)
        
        # 假人坐标
        self.dummy_x = QLineEdit()
        self.dummy_y = QLineEdit()
        self.dummy_z = QLineEdit()
        lidar_layout.addRow('假人坐标 X (m):', self.dummy_x)
        lidar_layout.addRow('假人坐标 Y (m):', self.dummy_y)
        lidar_layout.addRow('假人坐标 Z (m):', self.dummy_z)
        
        # 软件测量值
        lidar_layout.addRow(QLabel('<b>软件测量值（假人）:</b>'))
        self.measure_x = QLineEdit()
        self.measure_y = QLineEdit()
        self.measure_z = QLineEdit()
        lidar_layout.addRow('测量值 X (m):', self.measure_x)
        lidar_layout.addRow('测量值 Y (m):', self.measure_y)
        lidar_layout.addRow('测量值 Z (m):', self.measure_z)

        # ====== 新增：激光雷达姿态表格（去除NDT矫正前后） ======
        lidar_layout.addRow(QLabel('<b>激光雷达姿态：</b>'))
        self.roll = QLineEdit()
        self.roll.setPlaceholderText('请输入翻滚角（roll，保留三位小数）')
        lidar_layout.addRow('翻滚角（roll，保留小数点后三位）:', self.roll)
        self.pitch = QLineEdit()
        self.pitch.setPlaceholderText('请输入俯仰角（pitch，保留三位小数）')
        lidar_layout.addRow('俯仰角（pitch，保留小数点后三位）:', self.pitch)
        self.yaw = QLineEdit()
        self.yaw.setPlaceholderText('请输入偏航角（yaw，保留三位小数）')
        lidar_layout.addRow('偏航角（yaw，保留小数点后三位）:', self.yaw)

        lidar_layout.addRow(QLabel('<b>四元数：</b>'))
        self.qx = QLineEdit(); self.qx.setPlaceholderText('X')
        self.qy = QLineEdit(); self.qy.setPlaceholderText('Y')
        self.qz = QLineEdit(); self.qz.setPlaceholderText('Z')
        self.qw = QLineEdit(); self.qw.setPlaceholderText('ω')
        quat_layout = QHBoxLayout()
        quat_layout.addWidget(QLabel('X:'))
        quat_layout.addWidget(self.qx)
        quat_layout.addWidget(QLabel('Y:'))
        quat_layout.addWidget(self.qy)
        quat_layout.addWidget(QLabel('Z:'))
        quat_layout.addWidget(self.qz)
        quat_layout.addWidget(QLabel('ω:'))
        quat_layout.addWidget(self.qw)
        quat_widget = QWidget()
        quat_widget.setLayout(quat_layout)
        lidar_layout.addRow('四元数:', quat_widget)
        # ====== 新增内容结束 ======

        lidar_group.setLayout(lidar_layout)
        main_layout.addWidget(lidar_group)

        # 激光雷达装调与测试 故障诊断过程
        self.lidar_fault_group = QGroupBox('故障诊断过程')
        lidar_fault_layout = QFormLayout()
        self.lidar_fault_desc = QTextEdit(); self.lidar_fault_desc.setPlaceholderText('请输入故障现象描述...')
        self.lidar_fault_steps = QTextEdit(); self.lidar_fault_steps.setPlaceholderText('请输入诊断过程测量数据并分析...')
        self.lidar_fault_confirm = QTextEdit(); self.lidar_fault_confirm.setPlaceholderText('请输入故障确认内容...')
        self.lidar_fault_suggest = QTextEdit(); self.lidar_fault_suggest.setPlaceholderText('请输入故障机理分析及维修建议...')
        lidar_fault_layout.addRow('故障现象描述:', self.lidar_fault_desc)
        lidar_fault_layout.addRow('记录诊断过程测量数据并分析（记录关键步骤）:', self.lidar_fault_steps)
        lidar_fault_layout.addRow('故障确认:', self.lidar_fault_confirm)
        lidar_fault_layout.addRow('故障机理分析及维修建议:', self.lidar_fault_suggest)
        self.lidar_fault_group.setLayout(lidar_fault_layout)
        main_layout.addWidget(self.lidar_fault_group)

        # ====== 新增：传感器联合标定数据 ======
        calibration_group = QGroupBox('传感器联合标定数据')
        calibration_layout = QFormLayout()
        calibration_layout.setSpacing(10)
        calibration_layout.setContentsMargins(15, 20, 15, 15)

        # 摄像头标定点
        calibration_layout.addRow(QLabel('<b>摄像头标定点实际坐标值（单位：m）</b>'))
        self.cam_points = []
        for i in range(1, 5):
            x = QLineEdit(); x.setPlaceholderText(f'X{i}')
            y = QLineEdit(); y.setPlaceholderText(f'Y{i}')
            z = QLineEdit(); z.setPlaceholderText(f'Z{i}')
            self.cam_points.append((x, y, z))
            calibration_layout.addRow(f'点{i}：', self._make_hbox([x, y, z]))
        calibration_layout.addRow(QLabel('<b>摄像头标定点图像坐标值（单位：像素）</b>'))
        self.cam_img_points = []
        for i in range(1, 5):
            x = QLineEdit(); x.setPlaceholderText(f'X{i}')
            y = QLineEdit(); y.setPlaceholderText(f'Y{i}')
            self.cam_img_points.append((x, y))
            calibration_layout.addRow(f'点{i}：', self._make_hbox([x, y]))

        # 组合导航相关
        calibration_layout.addRow(QLabel('<b>组合导航相关</b>'))
        self.gnss_rear_center_left_right = QLineEdit(); self.gnss_rear_center_left_right.setPlaceholderText('单位：m')
        self.gnss_rear_center_height = QLineEdit(); self.gnss_rear_center_height.setPlaceholderText('单位：m')
        self.gnss_rear_center_angle = QLineEdit(); self.gnss_rear_center_angle.setPlaceholderText('单位：度')
        calibration_layout.addRow('对车辆后轴中心点的左右位置：', self.gnss_rear_center_left_right)
        calibration_layout.addRow('对车辆后轴中心点的高度：', self.gnss_rear_center_height)
        calibration_layout.addRow('对车辆后轴中心点的旋转角度：', self.gnss_rear_center_angle)

        # 毫米波雷达相关
        calibration_layout.addRow(QLabel('<b>毫米波雷达相关</b>'))
        self.mmw_left_right = QLineEdit(); self.mmw_left_right.setPlaceholderText('单位：m')
        self.mmw_front_rear = QLineEdit(); self.mmw_front_rear.setPlaceholderText('单位：m')
        self.mmw_height = QLineEdit(); self.mmw_height.setPlaceholderText('单位：m')
        self.mmw_max_distance = QLineEdit(); self.mmw_max_distance.setPlaceholderText('单位：m')
        calibration_layout.addRow('左右位置：', self.mmw_left_right)
        calibration_layout.addRow('前后位置：', self.mmw_front_rear)
        calibration_layout.addRow('高度：', self.mmw_height)
        calibration_layout.addRow('探测最远（可显示）距离：', self.mmw_max_distance)

        # 激光雷达相关
        calibration_layout.addRow(QLabel('<b>激光雷达相关</b>'))
        self.lidar_left_right = QLineEdit(); self.lidar_left_right.setPlaceholderText('单位：m')
        self.lidar_front_rear = QLineEdit(); self.lidar_front_rear.setPlaceholderText('单位：m')
        self.lidar_height = QLineEdit(); self.lidar_height.setPlaceholderText('单位：m')
        self.lidar_max_distance = QLineEdit(); self.lidar_max_distance.setPlaceholderText('单位：m')
        calibration_layout.addRow('左右位置：', self.lidar_left_right)
        calibration_layout.addRow('前后位置：', self.lidar_front_rear)
        calibration_layout.addRow('高度：', self.lidar_height)
        calibration_layout.addRow('探测最远（可显示）距离：', self.lidar_max_distance)

        # 新增主摄像头高度
        calibration_layout.addRow(QLabel('<b>主摄像头高度</b>'))
        self.main_camera_height = QLineEdit(); self.main_camera_height.setPlaceholderText('单位：m')
        calibration_layout.addRow('', self.main_camera_height)

        calibration_group.setLayout(calibration_layout)
        main_layout.addWidget(calibration_group)
        # ====== 新增内容结束 ======

        # 添加进度显示标签 (修改为进度条)
        progress_container = QWidget()
        progress_layout = QHBoxLayout(progress_container)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        
        # 将 QLabel 替换为 QProgressBar
        self.basic_progress_bar = QProgressBar()
        self.basic_progress_bar.setMaximum(len(self.check_items)) # 动态设置最大值
        self.basic_progress_bar.setFormat("基础检查进度: %v/%m")
        self.basic_progress_bar.setStyleSheet("font-weight: bold; color: #2c3e50;") # 沿用之前的样式
        
        # 添加总体进度标签
        self.base_sensor_progress_label = QLabel('基础检查+传感器安装角度进度: 0.0%')
        self.base_sensor_progress_label.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 12pt;")
        main_layout.addWidget(self.base_sensor_progress_label)

        # 新增：毫米波雷达测量数据进度条
        self.mmw_data_progress_label = QLabel('毫米波雷达测量数据进度: 0.0%')
        self.mmw_data_progress_label.setStyleSheet("color: #16a085; font-size: 11pt;")
        self.mmw_data_progress_label.hide()
        main_layout.addWidget(self.mmw_data_progress_label)

        # 新增：激光雷达测量数据进度条
        self.lidar_data_progress_label = QLabel('激光雷达装调与测试: 0.0%')
        self.lidar_data_progress_label.setStyleSheet("color: #8e44ad; font-size: 11pt;")
        self.lidar_data_progress_label.hide()
        main_layout.addWidget(self.lidar_data_progress_label)

        self.mmw_progress_label = QLabel('毫米波雷达调试与测试进度: 0.0%')
        self.mmw_progress_label.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 12pt;")
        self.mmw_progress_label.hide()
        main_layout.addWidget(self.mmw_progress_label)

        # 添加状态标签
        self.status_label = QLabel('等待发送...')
        self.status_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        
        progress_layout.addWidget(self.basic_progress_bar) # 添加进度条
        progress_layout.addWidget(self.status_label, 1)
        
        main_layout.addWidget(progress_container)
        
        # 添加按钮布局
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(20)
        
        # 添加重置按钮
        reset_button = QPushButton('重置所有选项')
        reset_button.setIcon(self.style().standardIcon(getattr(self.style().StandardPixmap, 'SP_BrowserReload')))
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        reset_button.clicked.connect(self.reset_all)
        
        # 添加生成工单按钮
        generate_button = QPushButton('生成工单')
        generate_button.setIcon(self.style().standardIcon(getattr(self.style().StandardPixmap, 'SP_DialogSaveButton')))
        generate_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #219653;
            }
        """)
        generate_button.clicked.connect(self.generate_report)
        
        button_layout.addWidget(reset_button)
        button_layout.addStretch(1)
        button_layout.addWidget(generate_button)
        
        main_layout.addWidget(button_container)
        
        # 设置UDP Socket
        self.socket = QUdpSocket(self)
        
        # 创建定时器用于定期发送更新
        self.broadcast_timer = QTimer(self)
        self.broadcast_timer.timeout.connect(self.update_progress)
        self.broadcast_timer.start(5000)  # 每秒发送一次更新

        # 预设诊断步骤
        self.diagnostic_presets = {
            "毫米波雷达上位机无数据": [
                "测量CAN分析仪CAN_H电压:",
                  "测量CAN分析仪CAN_L电压:", 
                  "测量毫米波DB9插头的CAN_H:",
                    "测量毫米波DB9插头的CAN_L:",
                    "使用示波器分析CAN总线波形情况:"
            ],
            "毫米波雷达工控机无数据": [
                "检查工控机电源状态",
                "检查系统进程是否正常",
                "检查网络连接状态",
                "检查数据接收模块是否正常",
                "检查系统日志是否有报错"
            ],
            "激光雷达上位机无数据": [
                "检查激光雷达接线盒电源指示灯是否正常",
                "检查以太网线是否正常连接且无损坏",
                "检查网络适配器IP地址设置是否正确",
                "检查激光雷达配置软件端口号设置是否正确",
            ],
            "激光雷达工控机无数据": [
                "检查工控机运行状态",
                "监控系统资源使用情况",
                "检查网络带宽使用情况",
                "分析数据处理延迟",
                "检查驱动程序状态"
            ],
            "组合导航上位机无数据": [
                "检查GPS信号接收状态",
                "验证IMU工作状态",
                "检查数据融合算法",
                "评估定位精度",
                "检查配置参数"
            ],
            "组合导航工控机无数据": [
                "检查系统运行状态",
                "验证导航数据完整性",
                "检查传感器同步状态",
                "测试数据更新频率",
                "检查系统日志"
            ]
        }
    
    def on_check_changed(self, state, item):
        """处理检查项勾选状态改变"""
        widgets = self.check_widgets[item]
        if len(widgets) == 3:  # 如果是电压检查项
            checkbox, text_input, voltage_input = widgets
            if state == Qt.CheckState.Checked:
                # 勾选时，禁用不合格说明输入框，但保持电压输入框可用
                text_input.setEnabled(False)
                text_input.setText(self.check_items[item])
                voltage_input.setEnabled(True)
            else:
                # 未勾选时，启用两个输入框
                text_input.setEnabled(True)
                text_input.clear()
                voltage_input.setEnabled(True)
                text_input.setFocus()
        else:  # 其他检查项
            checkbox, text_input = widgets
            if state == Qt.CheckState.Checked:
                text_input.setEnabled(False)
                text_input.setText(self.check_items[item])
            else:
                text_input.setEnabled(True)
                text_input.clear()
                text_input.setFocus()
        self.update_progress()

    def update_progress(self):
        """更新进度并发送到网络"""
        # 基础检查项进度
        completed = 0
        for item, widgets in self.check_widgets.items():
            checkbox = widgets[0]
            text_input = widgets[1]
            
            is_processed = False
            if checkbox.isChecked():
                is_processed = True
            else:
                # 如果未勾选，但用户输入了不合格说明
                if text_input.text().strip():
                    is_processed = True
                
                # 对于电压检查项，如果未勾选，但电压输入框有内容，也算作已处理
                if item == "车辆低压蓄电池电压" and len(widgets) == 3:
                    voltage_input = widgets[2]
                    if voltage_input.text().strip():
                        is_processed = True
            
            if is_processed:
                completed += 1

        total = len(self.check_widgets)
        self.basic_progress_bar.setValue(completed)
        self.basic_progress_bar.setFormat(f'基础检查进度: {completed}/{total}')
        base_progress = completed / total

        # 传感器角度进度（8项）
        sensor_fields = [self.mmw_angle, self.left_lidar_angle, self.right_lidar_angle, self.center_lidar_angle,
                         self.front_camera_angle, self.left_camera_angle, self.right_camera_angle]
        sensor_progress = sum(1 for f in sensor_fields if f.text().strip()) / len(sensor_fields)

        # 毫米波雷达测量数据进度
        mmw_fields = [
            self.reflector_x, self.reflector_y, self.mmw_real_id, self.mmw_dist_lat, self.mmw_dist_long,
            self.mmw_vlat, self.mmw_vlong, self.can_raw_data, self.can_real_id, self.can_dist_lat,
            self.can_dist_long, self.can_vlat, self.can_vlong
        ]
        mmw_filled = 0
        for f in mmw_fields:
            if hasattr(f, 'text'):
                if f.text().strip():
                    mmw_filled += 1
            else:
                if f.toPlainText().strip():
                    mmw_filled += 1
        mmw_progress_data = mmw_filled / len(mmw_fields)
        # 毫米波雷达故障诊断过程进度
        mmw_fault_fields = [self.mmw_fault_desc, self.mmw_fault_steps, self.mmw_fault_confirm, self.mmw_fault_suggest]
        mmw_fault_filled = sum(1 for f in mmw_fault_fields if f.toPlainText().strip())
        mmw_fault_progress = mmw_fault_filled / 4
        mmw_progress = mmw_progress_data * 0.6 + mmw_fault_progress * 0.4

        # 激光雷达测量数据进度
        lidar_fields = [self.dummy_x, self.dummy_y, self.dummy_z, self.measure_x, self.measure_y, self.measure_z,
                        self.roll, self.pitch, self.yaw, self.qx, self.qy, self.qz, self.qw]
        lidar_filled = sum(1 for f in lidar_fields if f.text().strip())
        lidar_progress_data = lidar_filled / len(lidar_fields)
        # 激光雷达故障诊断过程进度
        lidar_fault_fields = [self.lidar_fault_desc, self.lidar_fault_steps, self.lidar_fault_confirm, self.lidar_fault_suggest]
        lidar_fault_filled = sum(1 for f in lidar_fault_fields if f.toPlainText().strip())
        lidar_fault_progress = lidar_fault_filled / 4
        lidar_progress = lidar_progress_data * 0.6 + lidar_fault_progress * 0.4

        # 新权重分配
        # 基础检查+传感器安装角度合计10%
        base_sensor_weight = 0.10
        mmw_weight = 0.30
        lidar_weight = 0.30
        calibration_weight = 0.30

        # 传感器联合标定进度
        calibration_fields = []
        for x, y, z in self.cam_points:
            calibration_fields.extend([x, y, z])
        for x, y in self.cam_img_points:
            calibration_fields.extend([x, y])
        calibration_fields.extend([
            self.gnss_rear_center_left_right, self.gnss_rear_center_height, self.gnss_rear_center_angle,
            self.mmw_left_right, self.mmw_front_rear, self.mmw_height, self.mmw_max_distance,
            self.lidar_left_right, self.lidar_front_rear, self.lidar_height, self.lidar_max_distance,
            self.main_camera_height
        ])
        calibration_filled = sum(1 for f in calibration_fields if f.text().strip())
        calibration_progress = calibration_filled / len(calibration_fields)

        # 基础检查+传感器安装角度合并
        base_sensor_progress = (base_progress + sensor_progress) / 2 if (base_progress + sensor_progress) > 0 else 0

        # 总体进度
        total_progress = (base_sensor_progress * base_sensor_weight +
                          mmw_progress * mmw_weight +
                          lidar_progress * lidar_weight +
                          calibration_progress * calibration_weight)
        
        # 更新分项进度标签
        # 新增激光雷达调试与测试进度条
        if not hasattr(self, 'lidar_progress_label'):
            self.lidar_progress_label = QLabel('激光雷达调试与测试进度: 0.0%')
            self.lidar_progress_label.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 12pt;")
            self.lidar_progress_label.hide()
            self.mmw_progress_label.parentWidget().layout().addWidget(self.lidar_progress_label)

        # 只显示当前阶段进度条
        if base_sensor_progress < 1.0:
            self.base_sensor_progress_label.show()
            self.base_sensor_progress_label.setText(f"基础检查+传感器安装角度进度: {base_sensor_progress * 100:.1f}%")
            self.mmw_data_progress_label.hide()
            self.lidar_data_progress_label.hide()
            self.mmw_progress_label.hide()
            self.lidar_progress_label.hide()
        else:
            self.base_sensor_progress_label.hide()
            # 只要角反射器坐标有输入就显示毫米波雷达相关进度条，否则隐藏
            if self.reflector_x.text().strip() or self.reflector_y.text().strip():
                self.mmw_data_progress_label.show()
                self.mmw_data_progress_label.setText(f"毫米波雷达测量数据进度: {mmw_progress_data * 100:.1f}%")
                self.mmw_progress_label.show()
                self.mmw_progress_label.setText(f"毫米波雷达调试与测试进度: {mmw_progress * 100:.1f}%")
            else:
                self.mmw_data_progress_label.hide()
                self.mmw_progress_label.hide()
            # 激光雷达进度条逻辑保持原样
            self.lidar_data_progress_label.show()
            self.lidar_data_progress_label.setText(f"激光雷达测量数据进度: {lidar_progress_data * 100:.1f}%")
            if lidar_progress < 1.0:
                self.lidar_progress_label.show()
                self.lidar_progress_label.setText(f"激光雷达调试与测试进度: {lidar_progress * 100:.1f}%")
            else:
                self.lidar_progress_label.hide()
        
        # 准备要发送的数据
        data = {
            "base_check": {
                "total_items": total,
                "completed_items": completed,
                "checked_items": [key for key, widgets in self.check_widgets.items() if widgets[0].isChecked()]
            },
            # 已删除故障模块相关内容
            "total_progress": total_progress,
            "mmw_test_progress": mmw_progress,
            "lidar_measure_progress": lidar_progress,
            "sensor_fusion_progress": calibration_progress
        }
        
        # 将数据转换为JSON并发送
        message = json.dumps(data).encode('utf-8')
        print(f"准备发送数据到监控端，数据大小: {len(message)} 字节")
        
        target_ip = self.ip_input.text().strip()
        if target_ip:
            # 验证IP地址格式
            ip_parts = target_ip.split('.')
            if len(ip_parts) != 4 or not all(part.isdigit() and 0 <= int(part) <= 255 for part in ip_parts):
                print(f"无效的IP地址格式: {target_ip}")
                return
                
            try:
                print(f"尝试发送到IP: {target_ip}")
                address = QHostAddress(target_ip)
                if not address.isNull():
                    bytes_sent = self.socket.writeDatagram(message, address, 45454)
                    print(f"发送结果: {bytes_sent} 字节")
                    if bytes_sent > 0:
                        print("数据发送成功！")
                    else:
                        print("发送失败，返回字节数为0")
                else:
                    print(f"无效的IP地址: {target_ip}")
            except Exception as e:
                print(f"发送过程中出现错误: {str(e)}")
        else:
            print("未设置目标IP地址")

    def reset_all(self):
        """重置所有复选框和输入框"""
        # 重置检查项
        for item, widgets in self.check_widgets.items():
            widgets[0].setChecked(False)
            widgets[1].clear()
            widgets[1].setEnabled(True)
        self.update_progress()
        
        # 清空基本信息
        self.ip_input.clear()
        self.vehicle_info.clear()
        self.customer_info.clear()
        self.date.setText(datetime.now().strftime('%Y.%m.%d'))

        # 清空传感器角度信息
        self.mmw_angle.clear()
        self.left_lidar_angle.clear()
        self.right_lidar_angle.clear()
        self.center_lidar_angle.clear()
        self.front_camera_angle.clear()
        self.left_camera_angle.clear()
        self.right_camera_angle.clear()
        
        # 清空毫米波雷达数据
        self.reflector_x.clear()
        self.reflector_y.clear()
        self.mmw_real_id.clear()
        self.mmw_dist_lat.clear()
        self.mmw_dist_long.clear()
        self.mmw_vlat.clear()
        self.mmw_vlong.clear()
        self.can_raw_data.clear()
        self.can_real_id.clear()
        self.can_dist_lat.clear()
        self.can_dist_long.clear()
        self.can_vlat.clear()
        self.can_vlong.clear()
        
        # 清空激光雷达数据
        self.dummy_x.clear()
        self.dummy_y.clear()
        self.dummy_z.clear()
        self.measure_x.clear()
        self.measure_y.clear()
        self.measure_z.clear()
        
        # 清空所有故障模块
        # 故障相关方法全部删除

    def generate_report(self):
        """生成工单报告"""
        date_str = self.date.text() or datetime.now().strftime('%Y.%m.%d')
        
        try:
            datetime.strptime(date_str, '%Y.%m.%d')
        except ValueError:
            QMessageBox.warning(self, '警告', '日期格式不正确，请使用YYYY.MM.DD格式')
            return
        
        report = f"""车辆信息：{self.vehicle_info.text()}
客户信息：{self.customer_info.text()}
日期：{date_str}

传感器安装角度：
毫米波雷达安装角度：{self.mmw_angle.text() or "未填写"}
左侧激光雷达安装角度：{self.left_lidar_angle.text() or "未填写"}
右侧激光雷达安装角度：{self.right_lidar_angle.text() or "未填写"}
中间激光雷达安装角度：{self.center_lidar_angle.text() or "未填写"}
前摄像头安装角度：{self.front_camera_angle.text() or "未填写"}
左摄像头安装角度：{self.left_camera_angle.text() or "未填写"}
右摄像头安装角度：{self.right_camera_angle.text() or "未填写"}

基础检查项："""
        
        # 添加基础检查项结果
        for item in self.check_items.keys():
            checkbox, text_input = self.check_widgets[item]
            status = "合格" if checkbox.isChecked() else "不合格"
            description = text_input.text()
            report += f"\n{item}：{status} - {description}"
        
        # 添加毫米波雷达测量数据
        report += "\n\n毫米波雷达装调与测试："
        report += f"\n角反射器坐标：X={self.reflector_x.text()}, Y={self.reflector_y.text()}"
        report += "\n\n大陆雷达软件测量值（角反）："
        report += f"\n障碍物真实 ID：{self.mmw_real_id.text()}"
        report += f"\nDistLat/m：{self.mmw_dist_lat.text()}"
        report += f"\nDistLong/m：{self.mmw_dist_long.text()}"
        report += f"\nVlat：{self.mmw_vlat.text()}"
        report += f"\nVlong：{self.mmw_vlong.text()}"
        
        report += "\n\nUSB_CAN_TOOL软件解析后值："
        report += f"\n帧原始数据：\n{self.can_raw_data.toPlainText()}"
        report += f"\n障碍物真实 ID：{self.can_real_id.text()}"
        report += f"\nDistLat/m：{self.can_dist_lat.text()}"
        report += f"\nDistLong/m：{self.can_dist_long.text()}"
        report += f"\nVlat：{self.can_vlat.text()}"
        report += f"\nVlong：{self.can_vlong.text()}"
        # 毫米波雷达装调与测试 故障诊断过程
        report += "\n\n【毫米波雷达装调与测试 故障诊断过程】"
        report += f"\n故障现象描述：\n{self.mmw_fault_desc.toPlainText()}"
        report += f"\n记录诊断过程测量数据并分析（记录关键步骤）：\n{self.mmw_fault_steps.toPlainText()}"
        report += f"\n故障确认：\n{self.mmw_fault_confirm.toPlainText()}"
        report += f"\n故障机理分析及维修建议：\n{self.mmw_fault_suggest.toPlainText()}"
        
        # 添加激光雷达测量数据
        report += f"\n\n激光雷达装调与测试："
        report += f"\n假人坐标：X={self.dummy_x.text()}, Y={self.dummy_y.text()}, Z={self.dummy_z.text()}"
        report += f"\n软件测量值（假人）：X={self.measure_x.text()}, Y={self.measure_y.text()}, Z={self.measure_z.text()}"
        # 激光雷达装调与测试 故障诊断过程
        report += "\n\n【激光雷达装调与测试 故障诊断过程】"
        report += f"\n故障现象描述：\n{self.lidar_fault_desc.toPlainText()}"
        report += f"\n记录诊断过程测量数据并分析（记录关键步骤）：\n{self.lidar_fault_steps.toPlainText()}"
        report += f"\n故障确认：\n{self.lidar_fault_confirm.toPlainText()}"
        report += f"\n故障机理分析及维修建议：\n{self.lidar_fault_suggest.toPlainText()}"

        # ====== 新增：传感器联合标定数据 ======
        report += "\n\n传感器联合标定数据："
        report += "\n【摄像头标定点实际坐标值（单位：m）】"
        for i, (x, y, z) in enumerate(self.cam_points, 1):
            report += f"\n点{i}：X={x.text()}, Y={y.text()}, Z={z.text()}"
        report += "\n【摄像头标定点图像坐标值（单位：像素）】"
        for i, (x, y) in enumerate(self.cam_img_points, 1):
            report += f"\n点{i}：X={x.text()}, Y={y.text()}"
        report += f"\n【组合导航相关】"
        report += f"\n对车辆后轴中心点的左右位置：{self.gnss_rear_center_left_right.text()}"
        report += f"\n对车辆后轴中心点的高度：{self.gnss_rear_center_height.text()}"
        report += f"\n对车辆后轴中心点的旋转角度：{self.gnss_rear_center_angle.text()}"
        report += f"\n【毫米波雷达相关】"
        report += f"\n左右位置：{self.mmw_left_right.text()}"
        report += f"\n前后位置：{self.mmw_front_rear.text()}"
        report += f"\n高度：{self.mmw_height.text()}"
        report += f"\n探测最远（可显示）距离：{self.mmw_max_distance.text()}"
        report += f"\n【激光雷达相关】"
        report += f"\n左右位置：{self.lidar_left_right.text()}"
        report += f"\n前后位置：{self.lidar_front_rear.text()}"
        report += f"\n高度：{self.lidar_height.text()}"
        report += f"\n探测最远（可显示）距离：{self.lidar_max_distance.text()}"
        # 去掉激光雷达相对组合导航X/Y/Z轴的旋转角度
        # 新增主摄像头高度
        report += f"\n主摄像头高度：{self.main_camera_height.text()}"
        # 传感器联合标定数据 故障诊断过程
        report += "\n\n【传感器联合标定数据 故障诊断过程】"
        report += f"\n故障现象描述：\n{self.calib_fault_desc.toPlainText()}"
        report += f"\n记录诊断过程测量数据并分析（记录关键步骤）：\n{self.calib_fault_steps.toPlainText()}"
        report += f"\n故障确认：\n{self.calib_fault_confirm.toPlainText()}"
        report += f"\n故障机理分析及维修建议：\n{self.calib_fault_suggest.toPlainText()}"
        # ====== 新增内容结束 ======
        
        # 添加故障现象
        # 已删除故障模块相关内容
        
        # 生成文件名（使用日期）
        filename = f"{date_str}-工单.txt"
        filepath = os.path.join(self.data_dir, filename)
        
        # 保存报告到文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report)
            
            # 准备要发送的数据
            save_dir = os.path.join("C:/MonitorSystem", "reports")
            report_data = {
                "type": "report",
                "filename": filename,
                "content": report,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "save_path": save_dir,
                "create_dir": True  # 告诉监控端需要创建目录
            }
            
            # 将工单数据转换为JSON并发送
            message = json.dumps(report_data).encode('utf-8')
            print(f"准备发送数据到监控端，数据大小: {len(message)} 字节")
            
            success = False
            target_ip = self.ip_input.text().strip()
            if target_ip:
                try:
                    print(f"尝试发送到IP: {target_ip}")
                    address = QHostAddress(target_ip)
                    if not address.isNull():
                        bytes_sent = self.socket.writeDatagram(message, address, 45454)
                        print(f"发送结果: {bytes_sent} 字节")
                        if bytes_sent > 0:
                            success = True
                            print("数据发送成功！")
                            QMessageBox.information(self, '成功', 
                                f'工单已保存到本地: {filepath}\n'
                                f'已发送到监控端 {target_ip}，端口：45454\n'
                                f'监控端将保存到: {save_dir}\n'
                                f'注意：请确保监控端程序正在运行并正确接收数据。\n'
                                f'如果工单未出现在目标文件夹中，请检查：\n'
                                f'1. 监控端程序是否正在运行\n'
                                f'2. 是否正在监听45454端口\n'
                                f'3. 防火墙是否允许UDP通信')
                        else:
                            print("发送失败，返回字节数为0")
                            QMessageBox.warning(self, '警告', f'工单已保存到: {filepath}\n但发送到监控端失败，请检查网络连接')
                    else:
                        print(f"无效的IP地址: {target_ip}")
                        QMessageBox.warning(self, '警告', f'无效的IP地址: {target_ip}')
                except Exception as e:
                    print(f"发送过程中出现错误: {str(e)}")
                    QMessageBox.warning(self, '警告', f'发送过程中出现错误: {str(e)}')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存工单时出错: {e}')

    def _make_hbox(self, widgets):
        hbox = QHBoxLayout()
        for w in widgets:
            hbox.addWidget(w)
        container = QWidget()
        container.setLayout(hbox)
        return container

def main():
    # 设置 Qt 平台插件路径，解决中文用户名导致的问题
    # 请根据您的实际错误信息核对此路径是否正确
    qt_plugin_path = "C:/Users/<USER>/AppData/Local/Packages/PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0/LocalCache/local-packages/Python312/site-packages/PyQt5/Qt5/plugins"
    QCoreApplication.addLibraryPath(qt_plugin_path)

    app = QApplication(sys.argv)
    window = OperatorWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()