package operator.mmwave.data

import java.text.SimpleDateFormat
import java.util.*

/**
 * 基本信息数据类
 */
data class BasicInfo(
    val vehicleVin: String = "",
    val operatorName: String = "",
    val date: String = SimpleDateFormat("yyyy.MM.dd", Locale.getDefault()).format(Date()),
    val ipAddress: String = ""
)

/**
 * 传感器角度数据类
 */
data class SensorAngles(
    val mmwAngle: String = "",
    val leftLidarAngle: String = "",
    val rightLidarAngle: String = "",
    val centerLidarAngle: String = "",
    val frontCameraAngle: String = "",
    val leftCameraAngle: String = "",
    val rightCameraAngle: String = ""
)
