package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import operator.mmwave.data.CheckItem

/**
 * 检查项列表组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CheckItemsSection(
    checkItems: Map<String, CheckItem>,
    onCheckItemChange: (String, CheckItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "基础检查项",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.heightIn(max = 400.dp)
            ) {
                items(checkItems.toList()) { (itemName, checkItem) ->
                    CheckItemRow(
                        itemName = itemName,
                        checkItem = checkItem,
                        onCheckItemChange = { updatedItem ->
                            onCheckItemChange(itemName, updatedItem)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 单个检查项行组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CheckItemRow(
    itemName: String,
    checkItem: CheckItem,
    onCheckItemChange: (CheckItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (checkItem.isChecked) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = checkItem.isChecked,
                    onCheckedChange = { isChecked ->
                        onCheckItemChange(
                            checkItem.copy(
                                isChecked = isChecked,
                                customDescription = if (isChecked) checkItem.defaultDescription else ""
                            )
                        )
                    }
                )
                
                Text(
                    text = itemName,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 电压输入框（仅用于电压检查项）
            if (itemName == "车辆低压蓄电池电压") {
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = checkItem.voltageValue,
                    onValueChange = { voltage ->
                        onCheckItemChange(checkItem.copy(voltageValue = voltage))
                    },
                    label = { Text("电压值(V)") },
                    placeholder = { Text("请输入电压值") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            // 输入框 - 与Python程序逻辑一致
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedTextField(
                value = if (checkItem.isChecked) checkItem.defaultDescription else checkItem.customDescription,
                onValueChange = { text ->
                    if (!checkItem.isChecked) {
                        // 只有在未勾选时才允许编辑
                        onCheckItemChange(checkItem.copy(customDescription = text))
                    }
                },
                label = {
                    Text(if (checkItem.isChecked) "检查结果" else "不合格情况说明")
                },
                placeholder = {
                    Text(if (checkItem.isChecked) "已勾选" else "如不合格，请在此输入具体情况")
                },
                enabled = !checkItem.isChecked, // 勾选时禁用，与Python程序一致
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 3
            )
        }
    }
}
