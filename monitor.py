import sys
import json
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QProgressBar, QPushButton)
from PyQt6.QtCore import Qt
from PyQt6.QtNetwork import QUdpSocket, QHostAddress

class MonitorWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('')
        self.setGeometry(100, 100, 600, 400) 
        
        # 创建主容器
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #a1eafb;
            }
            QWidget {
                background-color: #defcf9;
            }
            QPushButton {
                background-color: #0066cc;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0052a3;
            }
            QPushButton:pressed {
                background-color:#f7fbfc;
            }
            QProgressBar {
                border: 1px solid #404040;
                border-radius: 6px;
                text-align: center;
                height: 25px;
                background-color:#fdfdfd;
                color: black;
            }
            QProgressBar::chunk {
                background-color: #14ffec;
                border-radius: 5px;
            }
        """)
        
        # 启动后自动置顶
        self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint, True)
        self.show()
        
        # 总体进度条
        self.total_progress_bar = QProgressBar()
        self.total_progress_bar.setMaximum(100)
        self.total_progress_bar.setFormat("总体进度条%p%")
        self.total_progress_bar.setStyleSheet("""
            QProgressBar {
                height: 30px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.total_progress_bar)

        # 毫米波雷达调试与测试进度条
        self.mmw_test_progress_bar = QProgressBar()
        self.mmw_test_progress_bar.setMaximum(100)
        self.mmw_test_progress_bar.setFormat("毫米波雷达调试与测试进度%p%")
        self.mmw_test_progress_bar.setStyleSheet("color: #2980b9; font-size: 12pt; height: 25px;")
        self.mmw_test_progress_bar.hide()
        layout.addWidget(self.mmw_test_progress_bar)

        # 激光雷达测量进度条
        self.lidar_measure_progress_bar = QProgressBar()
        self.lidar_measure_progress_bar.setMaximum(100)
        self.lidar_measure_progress_bar.setFormat("激光雷达装调与测试进度%p%")
        self.lidar_measure_progress_bar.setStyleSheet("color: #8e44ad; font-size: 12pt; height: 25px;")
        self.lidar_measure_progress_bar.hide()
        layout.addWidget(self.lidar_measure_progress_bar)

        # 多传感器融合进度条
        self.sensor_fusion_progress_bar = QProgressBar()
        self.sensor_fusion_progress_bar.setMaximum(100)
        self.sensor_fusion_progress_bar.setFormat("多传感器融合进度%p%")
        self.sensor_fusion_progress_bar.setStyleSheet("color: #e67e22; font-size: 12pt; height: 25px;")
        self.sensor_fusion_progress_bar.hide()
        layout.addWidget(self.sensor_fusion_progress_bar)
        
        # 基础检查进度条
        self.basic_progress_bar = QProgressBar()
        self.basic_progress_bar.setMaximum(22)
        self.basic_progress_bar.setFormat("基础检查进度%v/%m")
        layout.addWidget(self.basic_progress_bar)
        
        # 故障模块进度条容器
        self.fault_modules_container = QWidget()
        self.fault_modules_layout = QVBoxLayout(self.fault_modules_container)
        self.fault_modules_layout.setSpacing(12)
        layout.addWidget(self.fault_modules_container)
        
        # 存储故障模块的进度条
        self.fault_modules = {}
        
        # 添加底部空白
        layout.addStretch()
        
        # 设置UDP Socket
        self.socket = QUdpSocket(self)
        self.socket.bind(QHostAddress("0.0.0.0"), 45454)
        self.socket.readyRead.connect(self.process_pending_datagrams)
    
    def process_pending_datagrams(self):
        """处理接收到的UDP数据包"""
        while self.socket.hasPendingDatagrams():
            datagram = self.socket.receiveDatagram()
            if datagram.isValid():
                try:
                    data = json.loads(datagram.data().data().decode('utf-8'))
                    self.update_ui(data)
                except Exception:
                    pass
    
    def update_ui(self, data):
        """更新界面显示"""
        try:
            # 更新基础检查进度
            base_check = data["base_check"]
            self.basic_progress_bar.setValue(base_check["completed_items"])
            # 基础检查完成后自动隐藏
            total_progress = int(data["total_progress"] * 100)
            if base_check["completed_items"] >= self.basic_progress_bar.maximum() or total_progress >= 10:
                self.basic_progress_bar.hide()
            else:
                self.basic_progress_bar.show()
            
            # 更新总体进度
            total_progress = int(data["total_progress"] * 100)
            self.total_progress_bar.setValue(total_progress)

            # 更新毫米波雷达调试与测试进度
            if "mmw_test_progress" in data and (base_check["completed_items"] >= self.basic_progress_bar.maximum() or total_progress >= 10):
                mmw_val = int(data["mmw_test_progress"] * 100)
                self.mmw_test_progress_bar.setValue(mmw_val)
                if mmw_val >= 100:
                    self.mmw_test_progress_bar.hide()
                else:
                    self.mmw_test_progress_bar.show()
            else:
                self.mmw_test_progress_bar.hide()

            # 更新激光雷达测量进度
            if "lidar_measure_progress" in data and int(data["mmw_test_progress"] * 100) >= 100:
                lidar_val = int(data["lidar_measure_progress"] * 100)
                self.lidar_measure_progress_bar.setValue(lidar_val)
                if lidar_val >= 100:
                    self.lidar_measure_progress_bar.hide()
                else:
                    self.lidar_measure_progress_bar.show()
            else:
                self.lidar_measure_progress_bar.hide()

            # 更新多传感器融合进度
            if "sensor_fusion_progress" in data and int(data.get("lidar_measure_progress", 0) * 100) >= 100:
                fusion_val = int(data["sensor_fusion_progress"] * 100)
                self.sensor_fusion_progress_bar.setValue(fusion_val)
                if fusion_val >= 100:
                    self.sensor_fusion_progress_bar.hide()
                else:
                    self.sensor_fusion_progress_bar.show()
            else:
                self.sensor_fusion_progress_bar.hide()
            
            # 更新故障模块进度
            current_types = {module["type"] for module in data["fault_modules"] if module["type"]}
            for module_type in list(self.fault_modules.keys()):
                if module_type not in current_types:
                    progress_bar = self.fault_modules[module_type]
                    progress_bar.setParent(None)
                    del self.fault_modules[module_type]
            
            # 更新或添加故障模块
            for module in data["fault_modules"]:
                module_type = module["type"]
                if not module_type:
                    continue
                
                if module_type not in self.fault_modules:
                    # 创建新的进度条
                    progress_bar = QProgressBar()
                    progress_bar.setMaximum(100)
                    progress_bar.setFormat(f"{module_type}: %p%")
                    
                    # 将进度条添加到布局中
                    self.fault_modules_layout.addWidget(progress_bar)
                    
                    # 保存引用
                    self.fault_modules[module_type] = progress_bar
                
                # 更新进度
                progress_bar = self.fault_modules[module_type]
                progress = int(module["progress"] * 100)
                progress_bar.setValue(progress)
                
        except Exception:
            pass

def main():
    app = QApplication(sys.argv)
    window = MonitorWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()