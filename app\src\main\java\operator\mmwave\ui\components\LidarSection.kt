package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import operator.mmwave.data.LidarData
import operator.mmwave.data.LidarFaultDiagnosis

/**
 * 激光雷达测试组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LidarSection(
    lidarData: LidarData,
    lidarFaultDiagnosis: LidarFaultDiagnosis,
    onLidarDataChange: (LidarData) -> Unit,
    onLidarFaultDiagnosisChange: (LidarFaultDiagnosis) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "激光雷达装调与测试",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 假人距中间激光雷达实际坐标
            Text(
                text = "假人距中间激光雷达实际坐标",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = lidarData.dummyX,
                    onValueChange = { onLidarDataChange(lidarData.copy(dummyX = it)) },
                    label = { Text("X (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = lidarData.dummyY,
                    onValueChange = { onLidarDataChange(lidarData.copy(dummyY = it)) },
                    label = { Text("Y (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = lidarData.dummyZ,
                    onValueChange = { onLidarDataChange(lidarData.copy(dummyZ = it)) },
                    label = { Text("Z (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }
            
            // 激光雷达软件测量假人坐标信息
            Text(
                text = "激光雷达软件测量假人坐标信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = lidarData.measureX,
                    onValueChange = { onLidarDataChange(lidarData.copy(measureX = it)) },
                    label = { Text("测量值 X (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = lidarData.measureY,
                    onValueChange = { onLidarDataChange(lidarData.copy(measureY = it)) },
                    label = { Text("测量值 Y (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
                
                OutlinedTextField(
                    value = lidarData.measureZ,
                    onValueChange = { onLidarDataChange(lidarData.copy(measureZ = it)) },
                    label = { Text("测量值 Z (m)") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true
                )
            }

            
            // 故障诊断过程
            Text(
                text = "故障诊断过程",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp)
            )
            
            OutlinedTextField(
                value = lidarFaultDiagnosis.faultDescription,
                onValueChange = { onLidarFaultDiagnosisChange(lidarFaultDiagnosis.copy(faultDescription = it)) },
                label = { Text("故障现象描述") },
                placeholder = { Text("请输入故障现象描述...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
            
            OutlinedTextField(
                value = lidarFaultDiagnosis.diagnosticSteps,
                onValueChange = { onLidarFaultDiagnosisChange(lidarFaultDiagnosis.copy(diagnosticSteps = it)) },
                label = { Text("记录诊断过程测量数据并分析（记录关键步骤）") },
                placeholder = { Text("请输入诊断过程测量数据并分析...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 6
            )
            
            OutlinedTextField(
                value = lidarFaultDiagnosis.faultConfirmation,
                onValueChange = { onLidarFaultDiagnosisChange(lidarFaultDiagnosis.copy(faultConfirmation = it)) },
                label = { Text("故障确认") },
                placeholder = { Text("请输入故障确认内容...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
            
            OutlinedTextField(
                value = lidarFaultDiagnosis.repairSuggestion,
                onValueChange = { onLidarFaultDiagnosisChange(lidarFaultDiagnosis.copy(repairSuggestion = it)) },
                label = { Text("故障机理分析及维修建议") },
                placeholder = { Text("请输入故障机理分析及维修建议...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                maxLines = 4
            )
        }
    }
}
