package operator.mmwave.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import operator.mmwave.data.ProgressData

/**
 * 进度显示组件
 */
@Composable
fun ProgressSection(
    progressData: ProgressData,
    statusMessage: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "进度监控",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                fontWeight = FontWeight.Bold
            )
            
            // 基础检查进度
            ProgressItem(
                label = "基础检查进度",
                progress = progressData.baseCheckProgress,
                showPercentage = false,
                progressText = "${(progressData.baseCheckProgress * 22).toInt()}/22"
            )
            
            // 传感器角度进度
            ProgressItem(
                label = "传感器角度进度",
                progress = progressData.sensorAngleProgress,
                showPercentage = true
            )
            
            // 基础检查+传感器角度合并进度
            val baseSensorProgress = (progressData.baseCheckProgress + progressData.sensorAngleProgress) / 2f
            if (baseSensorProgress < 1.0f) {
                ProgressItem(
                    label = "基础检查+传感器安装角度进度",
                    progress = baseSensorProgress,
                    showPercentage = true,
                    isHighlighted = true
                )
            }
            
            // 毫米波雷达相关进度
            if (progressData.mmwaveDataProgress > 0f || progressData.mmwaveFaultProgress > 0f) {
                ProgressItem(
                    label = "毫米波雷达测量数据进度",
                    progress = progressData.mmwaveDataProgress,
                    showPercentage = true,
                    color = MaterialTheme.colorScheme.secondary
                )
                
                val mmwProgress = progressData.mmwaveDataProgress * 0.6f + progressData.mmwaveFaultProgress * 0.4f
                ProgressItem(
                    label = "毫米波雷达调试与测试进度",
                    progress = mmwProgress,
                    showPercentage = true,
                    isHighlighted = true,
                    color = MaterialTheme.colorScheme.secondary
                )
            }
            
            // 激光雷达相关进度
            if (progressData.lidarDataProgress > 0f || progressData.lidarFaultProgress > 0f) {
                ProgressItem(
                    label = "激光雷达测量数据进度",
                    progress = progressData.lidarDataProgress,
                    showPercentage = true,
                    color = MaterialTheme.colorScheme.tertiary
                )
                
                val lidarProgress = progressData.lidarDataProgress * 0.6f + progressData.lidarFaultProgress * 0.4f
                if (lidarProgress < 1.0f) {
                    ProgressItem(
                        label = "激光雷达调试与测试进度",
                        progress = lidarProgress,
                        showPercentage = true,
                        isHighlighted = true,
                        color = MaterialTheme.colorScheme.tertiary
                    )
                }
            }
            
            // 标定数据进度
            if (progressData.calibrationProgress > 0f) {
                ProgressItem(
                    label = "传感器联合标定进度",
                    progress = progressData.calibrationProgress,
                    showPercentage = true,
                    color = MaterialTheme.colorScheme.error
                )
            }
            
            // 总体进度
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            ProgressItem(
                label = "总体进度",
                progress = progressData.totalProgress,
                showPercentage = true,
                isHighlighted = true,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "状态:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = statusMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 单个进度项组件
 */
@Composable
private fun ProgressItem(
    label: String,
    progress: Float,
    showPercentage: Boolean = true,
    progressText: String? = null,
    isHighlighted: Boolean = false,
    color: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = if (isHighlighted) 
                    MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold)
                else 
                    MaterialTheme.typography.bodySmall,
                color = if (isHighlighted) 
                    MaterialTheme.colorScheme.onPrimaryContainer
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = progressText ?: if (showPercentage) "${(progress * 100).toInt()}%" else "",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = if (isHighlighted) FontWeight.Bold else FontWeight.Normal,
                color = if (isHighlighted) 
                    MaterialTheme.colorScheme.onPrimaryContainer
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        LinearProgressIndicator(
            progress = progress.coerceIn(0f, 1f),
            modifier = Modifier
                .fillMaxWidth()
                .height(if (isHighlighted) 8.dp else 6.dp),
            color = color,
            trackColor = MaterialTheme.colorScheme.surfaceVariant
        )
    }
}
