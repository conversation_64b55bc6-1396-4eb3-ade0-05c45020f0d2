package operator.mmwave.data

/**
 * 摄像头标定点坐标（实际坐标）
 */
data class CameraCalibrationPoint(
    val x: String = "",
    val y: String = "",
    val z: String = ""
)

/**
 * 摄像头标定点坐标（图像坐标）
 */
data class CameraImagePoint(
    val x: String = "",
    val y: String = ""
)

/**
 * 传感器联合标定数据
 */
data class CalibrationData(
    // 摄像头标定点实际坐标值（4个点）
    val cameraPoints: List<CameraCalibrationPoint> = List(4) { CameraCalibrationPoint() },
    
    // 摄像头标定点图像坐标值（4个点）
    val cameraImagePoints: List<CameraImagePoint> = List(4) { CameraImagePoint() },
    
    // 组合导航相关
    val gnssRearCenterLeftRight: String = "",
    val gnssRearCenterHeight: String = "",
    val gnssRearCenterAngle: String = "",
    
    // 毫米波雷达相关
    val mmwLeftRight: String = "",
    val mmwFrontRear: String = "",
    val mmwHeight: String = "",
    val mmwMaxDistance: String = "",
    
    // 激光雷达相关
    val lidarLeftRight: String = "",
    val lidarFrontRear: String = "",
    val lidarHeight: String = "",
    val lidarMaxDistance: String = "",
    
    // 主摄像头高度
    val mainCameraHeight: String = ""
)
