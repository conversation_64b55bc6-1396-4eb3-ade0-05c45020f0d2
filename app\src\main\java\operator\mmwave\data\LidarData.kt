package operator.mmwave.data

/**
 * 激光雷达测量数据
 */
data class LidarData(
    // 假人坐标
    val dummyX: String = "",
    val dummyY: String = "",
    val dummyZ: String = "",
    
    // 软件测量值
    val measureX: String = "",
    val measureY: String = "",
    val measureZ: String = "",
    
    // 激光雷达姿态
    val roll: String = "",
    val pitch: String = "",
    val yaw: String = "",
    
    // 四元数
    val qx: String = "",
    val qy: String = "",
    val qz: String = "",
    val qw: String = ""
)

/**
 * 激光雷达故障诊断数据
 */
data class LidarFaultDiagnosis(
    val faultDescription: String = "",
    val diagnosticSteps: String = "",
    val faultConfirmation: String = "",
    val repairSuggestion: String = ""
)

/**
 * 激光雷达诊断预设步骤
 */
object LidarDiagnosticPresets {
    val lidarNoDataFromHost = listOf(
        "检查激光雷达接线盒电源指示灯是否正常",
        "检查以太网线是否正常连接且无损坏",
        "检查网络适配器IP地址设置是否正确",
        "检查激光雷达配置软件端口号设置是否正确"
    )
    
    val lidarNoDataFromPC = listOf(
        "检查工控机运行状态",
        "监控系统资源使用情况",
        "检查网络带宽使用情况",
        "分析数据处理延迟",
        "检查驱动程序状态"
    )
}
